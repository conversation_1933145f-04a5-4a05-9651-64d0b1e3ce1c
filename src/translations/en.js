export const en = {
  auth: {
    signIn: "Sign In",
    signOut: "Sign Out",
    profile: "Profile",
    signin: {
      title: "Sign in to your account",
      subtitle: "Welcome back! Please enter your details",
      email: "Email",
      emailPlaceholder: "<EMAIL>",
      emailRequired: "Please enter your email",
      emailInvalid: "Please enter a valid email",
      password: "Password",
      passwordPlaceholder: "••••••••",
      passwordRequired: "Please enter your password",
      rememberMe: "Remember me",
      forgotPassword: "Forgot password?",
      signInWithGoogle: "Sign in with Google",
      or: "or",
      noAccount: "Don't have an account?",
      createAccount: "Create an account",
      back: "Back",
      loading: "Signing in...",
      buttonText: "Sign in",
      registrationSuccess: "Registration successful! Please login with your credentials.",
      invalidCredentials: "Invalid email or password",
      errorOccurred: "An error occurred during login",
      googleSignInError: "Failed to sign in with Google"
    },
    signup: {
      title: "Create an account",
      subtitle: "Get started now. No credit card required.",
      fullName: "Full Name",
      fullNamePlaceholder: "<PERSON>",
      fullNameRequired: "Please enter your name",
      email: "Email",
      emailPlaceholder: "<EMAIL>",
      emailRequired: "Please enter your email",
      emailInvalid: "Please enter a valid email",
      password: "Password",
      passwordPlaceholder: "••••••••",
      passwordRequired: "Please enter your password",
      passwordMinLength: "Must be at least 8 characters",
      confirmPassword: "Confirm Password",
      confirmPasswordPlaceholder: "••••••••",
      confirmPasswordRequired: "Please confirm your password",
      passwordMismatch: "Passwords do not match",
      signupWithGoogle: "Sign up with Google",
      or: "or",
      back: "Back",
      alreadyHaveAccount: "Already have an account?",
      signInLink: "Sign in",
      privacyNotice: "See our",
      privacyPolicy: "Privacy Policy",
      and: "and",
      termsOfService: "Terms of Service",
      creatingAccount: "Creating account...",
      createAccountButton: "Create account"
    },
    forgotPassword: {
      title: "Forgot Password",
      subtitle: "Enter your email address and we'll send you a link to reset your password",
      email: "Email",
      emailPlaceholder: "<EMAIL>",
      emailRequired: "Please enter your email",
      emailInvalid: "Please enter a valid email",
      back: "Back",
      sendResetLink: "Send Reset Link",
      sending: "Sending...",
      successMessage: "Password reset link sent! Please check your email.",
      returnToLogin: "Return to login",
      errorMessage: "An error occurred while processing your request"
    }
  },
  app: {
    settings: {
      plans: {
        plan1001: {
          name: "Starter Pro",
          description: "Basic recruitment solution for small businesses and SMEs.",
          idealFor: "Small businesses and SMEs",
          features: [
            "Basic CV Pool (Limited)",
            "Basic AI CV Parsing",
            "Basic Reporting Tools",
            "Email Technical Support",
            "14-Day Free Trial"
          ],
        },
        plan1002: {
          name: "Professional",
          description: "Ideal solution for growing SMEs and mid-sized businesses.",
          idealFor: "Growing SMEs and Mid-Sized Businesses",
          features: [
            "Unlimited CV Pool",
            "AI CV Evaluation (Advanced)",
            "Advanced AI Role Matching",
            "Advanced Reporting and Analytics",
            "Organization Design Module",
            "Priority Email and Phone Support"
          ],
        },
        plan1003: {
          name: "Enterprise",
          description: "Custom solutions for large-scale businesses and corporate companies.",
          idealFor: "Corporate Companies and Large-Scale Businesses",
          features: [
            "All Professional Features",
            "Custom Integrations",
            "Priority Support",
            "Multi-Organization Design Module",
            "Dedicated Customer Manager",
            "Comprehensive Training and Premium Support"
          ],
        },
        monthly: "Monthly",
        yearly: "Yearly",
        weekly: "Weekly",
        ozelFiyat: "Custom Price",
        freeTrial: "14-Day Free Trial",
        popular: "Most Popular",
        contactSales: "Contact Sales Team",
        yeniPlanaGec: "Activate New Plan",
        cancel: "Cancel",
        title: "Choose the Plan That Best Fits Your Needs!",
        subTitle: "Select One of Our Plans Tailored for Your Business.",
        loading: "Loading pricing plans...",
        discount: "%17 Discount",
        discountInfo: "Save 17% with annual payment",
        yil: "year",
        ay: "month",
      }
    },
  }
};
