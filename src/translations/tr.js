export const tr = {
  auth: {
    signIn: "<PERSON><PERSON><PERSON> Yap",
    signOut: "<PERSON><PERSON><PERSON><PERSON><PERSON> Yap",
    profile: "Profil",
    signin: {
      title: "<PERSON><PERSON><PERSON><PERSON>nıza giriş yapın",
      subtitle: "Tekrar hoş geldiniz! Lütfen bilgilerinizi girin",
      email: "E-posta",
      emailPlaceholder: "<EMAIL>",
      emailRequired: "Lütfen e-posta adresinizi girin",
      emailInvalid: "<PERSON>ütfen geçerli bir e-posta adresi girin",
      password: "<PERSON><PERSON><PERSON>",
      passwordPlaceholder: "••••••••",
      passwordRequired: "Lütfen şifrenizi girin",
      rememberMe: "Beni hatırla",
      forgotPassword: "Şifremi unuttum?",
      signInWithGoogle: "Google ile giriş yap",
      or: "veya",
      noAccount: "Hesabınız yok mu?",
      createAccount: "<PERSON>sa<PERSON> oluşturun",
      back: "Geri",
      loading: "<PERSON><PERSON><PERSON> yapılıyor...",
      buttonText: "<PERSON><PERSON><PERSON> yap",
      registrationSuccess: "Kayıt başarılı! Lütfen bilgilerinizle giriş yapın.",
      invalidCredentials: "Geçersiz e-posta veya şifre",
      errorOccurred: "Giriş sırasında bir hata oluştu",
      googleSignInError: "Google ile giriş yapılırken hata oluştu"
    },
    signup: {
      title: "Hesap Oluştur",
      subtitle: "Hemen başlayın. Kredi kartı gerekmez.",
      fullName: "Ad Soyad",
      fullNamePlaceholder: "Ahmet Yılmaz",
      fullNameRequired: "Lütfen adınızı girin",
      email: "E-posta",
      emailPlaceholder: "<EMAIL>",
      emailRequired: "Lütfen e-posta adresinizi girin",
      emailInvalid: "Lütfen geçerli bir e-posta adresi girin",
      password: "Şifre",
      passwordPlaceholder: "••••••••",
      passwordRequired: "Lütfen şifrenizi girin",
      passwordMinLength: "En az 8 karakter olmalıdır",
      confirmPassword: "Şifre Tekrar",
      confirmPasswordPlaceholder: "••••••••",
      confirmPasswordRequired: "Lütfen şifrenizi tekrar girin",
      passwordMismatch: "Şifreler eşleşmiyor",
      signupWithGoogle: "Google ile Kaydol",
      or: "veya",
      back: "Geri",
      alreadyHaveAccount: "Zaten hesabınız var mı?",
      signInLink: "Giriş yapın",
      privacyNotice: "Bakınız:",
      privacyPolicy: "Gizlilik Politikası",
      and: "ve",
      termsOfService: "Kullanım Koşulları",
      creatingAccount: "Hesap oluşturuluyor...",
      createAccountButton: "Hesap oluştur"
    },
    forgotPassword: {
      title: "Şifremi Unuttum",
      subtitle: "E-posta adresinizi girin, size şifre sıfırlama bağlantısı göndereceğiz",
      email: "E-posta",
      emailPlaceholder: "<EMAIL>",
      emailRequired: "Lütfen e-posta adresinizi girin",
      emailInvalid: "Lütfen geçerli bir e-posta adresi girin",
      back: "Geri",
      sendResetLink: "Sıfırlama Bağlantısı Gönder",
      sending: "Gönderiliyor...",
      successMessage: "Şifre sıfırlama bağlantısı gönderildi! Lütfen e-postanızı kontrol edin.",
      returnToLogin: "Giriş sayfasına dön",
      errorMessage: "İsteğiniz işlenirken bir hata oluştu"
    }
  },
  appPub: {
    name: "TFLOUU",
    description: "Yapay Zeka Destekli İşe Alım Platformu",
    version: "1.0.0",
    author: {
      name: "Taner Subasi",
      email: "<EMAIL>"
    },
  },
  app: {
    settings: {
      plans: {
        plan1001: {
          name: "Starter Pro",
          description: "Küçük işletmeler ve KOBİ 'ler için temel işe alım çözümü.",
          idealFor: "Küçük işletmeler ve KOBİ 'ler",
          features: [
            "Temel CV Havuzu (Sınırlı)",
            "Temel AI CV Ayrıştırma",
            "Temel Raporlama Araçları",
            "E-posta ile Teknik Destek",
            "14 Gün Ücretsiz Deneme"
          ],
        },
        plan1002: {
          name: "Profesyonel",
          description: "Büyüyen KOBİ'ler ve orta ölçekli işletmeler için ideal çözüm.",
          idealFor: "Büyüyen KOBİ'ler ve Orta Ölçekli İşletmeler",
          features: [
            "Sınırsız CV Havuzu",
            "AI CV Değerlendirme (Gelişmiş)",
            "Gelişmiş AI Rol Eşleştirme",
            "Gelişmiş Raporlama ve Analitik",
            "Organizasyon Tasarımı Modülü",
            "Öncelikli E-posta ve Telefon Desteği"
          ],
        },
        plan1003: {
          name: "Kurumsal",
          description: "Büyük ölçekli işletmeler ve kurumsal şirketler için özel çözümler.",
          idealFor: "Kurumsal Şirketler ve Büyük Ölçekli İşletmeler",
          features: [
            "Tüm Profesyonel Özellikler",
            "Özel Entegrasyonlar",
            "Öncelikli Destek",
            "Çoklu Organizasyon Tasarımı Modülü",
            "Özel Müşteri Yöneticisi",
            "Kapsamlı Eğitim ve Premium Destek",
          ],
        },
        monthly: "Aylık",
        yearly: "Yıllık",
        weekly: "Haftalık",
        ozelFiyat: "Özel Fiyat",
        freeTrial: "14 Gün Ücretsiz Deneme",
        popular: "En Popüler",
        contactSales: "Satış Ekipleri ile İletişime Geçin",
        yeniPlanaGec: "Yeni Planı Etkinleştir",
        cancel: "İptal",
        title: "İhtiyaçlarınıza En Uygun Planı Seçin!",
        subTitle: "İşletmenize Özel Planlarımızdan Birini Seçin.",
        loading: "Fiyat planları yükleniyor...",
        discount: "%17 İndirim",
        discountInfo: "Yıllık ödemede %17 tasarruf",
        yil: "yil",
        ay: "ay",
      }
    },
  }
};
