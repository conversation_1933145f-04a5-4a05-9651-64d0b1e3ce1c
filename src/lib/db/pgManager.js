import { Pool } from "pg"
import { vars } from "@/lib/constants"
import { decryptConfig } from "@/lib/fnx/fnx.encrypt"
import clientPromise from '@/lib/db/mongodb';
const pools = new Map() // key: customerId

export async function getCustomerDB(customerId) {
    if (pools.has(customerId)) {
        return pools.get(customerId)
    }

    const mongoClient = await clientPromise;
    // MongoDB'den müşteri PG bilgilerini al
    const customer = await mongoClient
        .db(vars.db.dbName)
        .collection(vars.db.collection.customers)
        .findOne({ clientId: customerId })

    if (!customer || !customer.dbConn) {
        throw new Error(`PG config not found for customer: ${customerId}`)
    }
    let config = customer.dbConn;
    delete config.type; // MongoDB'den gelen fazlalık alanı sil
    console.log(`Found config for ${customerId}:`, config);
    const decryptedConfig = decryptConfig(config.encryptedData, config.iv, config.authTag) // Şifreyi çö<PERSON>
    console.log(`Decrypted config for ${customerId}:`, decryptedConfig);
    const pool = new Pool({
        host: decryptedConfig.host,
        port: decryptedConfig.port,
        database: decryptedConfig.database,
        user: decryptedConfig.user,
        password: decryptedConfig.password,
        // ssl: decryptedConfig.ssl,
        max: 10, // her müşteri için max 10 bağlantı
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 2000
    })

    // Bağlantı hatasında temizle
    pool.on("error", err => {
        console.error(`Postgres pool error for ${customerId}:`, err)
        pools.delete(customerId)
    })

    pools.set(customerId, pool)
    return pool
}
