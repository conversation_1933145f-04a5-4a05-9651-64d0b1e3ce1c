// Customer model schema and operations
import clientPromise from '../mongodb';
import { ObjectId } from 'mongodb';

const usersCollection = 'tourai.main.dim.users';
export const CustomerSchema = {
  _id: ObjectId,
  name: String,
  email: String,
  phone: String,
  company: String,
  position: String,
  industry: String,
  website: String,
  address: {
    street: String,
    city: String,
    state: String,
    country: String,
    zipCode: String,
  },
  socialMedia: {
    linkedin: String,
    twitter: String,
    facebook: String,
  },
  tags: [String],
  source: String, // 'website', 'referral', 'cold-call', 'email', 'social-media'
  status: String, // 'active', 'inactive', 'prospect', 'customer'
  priority: String, // 'low', 'medium', 'high'
  assignedTo: ObjectId, // reference to User
  createdBy: ObjectId, // reference to User
  notes: [
    {
      _id: ObjectId,
      content: String,
      createdBy: ObjectId,
      createdAt: Date,
      isPrivate: Boolean,
    }
  ],
  interactions: [
    {
      _id: ObjectId,
      type: String, // 'call', 'email', 'meeting', 'chat'
      description: String,
      createdBy: ObjectId,
      createdAt: Date,
      duration: Number, // in minutes
    }
  ],
  customFields: Object, // flexible custom data
  createdAt: Date,
  updatedAt: Date,
};

export class CustomerModel {
  static async findById(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection('customers').findOne({ _id: new ObjectId(id) });
  }

  static async findByEmail(email) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection('customers').findOne({ email });
  }

  static async create(customerData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const customer = {
      ...customerData,
      status: customerData.status || 'prospect',
      priority: customerData.priority || 'medium',
      tags: customerData.tags || [],
      address: customerData.address || {},
      socialMedia: customerData.socialMedia || {},
      notes: [],
      interactions: [],
      customFields: customerData.customFields || {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const result = await db.collection('customers').insertOne(customer);
    return { ...customer, _id: result.insertedId };
  }

  static async update(id, updateData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const result = await db.collection('customers').updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          ...updateData, 
          updatedAt: new Date() 
        } 
      }
    );
    
    return result;
  }

  static async findAll(filter = {}, options = {}) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = options;
    const skip = (page - 1) * limit;
    
    // Add search functionality
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { company: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Build aggregation pipeline for populated data
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: usersCollection,
          localField: 'assignedTo',
          foreignField: '_id',
          as: 'assignedUser'
        }
      },
      {
        $lookup: {
          from: usersCollection,
          localField: 'createdBy',
          foreignField: '_id',
          as: 'creator'
        }
      },
      { $unwind: { path: '$assignedUser', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$creator', preserveNullAndEmptyArrays: true } },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const customers = await db.collection('customers').aggregate(pipeline).toArray();
    const total = await db.collection('customers').countDocuments(filter);
    
    return {
      customers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  static async addNote(customerId, noteData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const note = {
      _id: new ObjectId(),
      ...noteData,
      createdAt: new Date(),
    };
    
    return await db.collection('customers').updateOne(
      { _id: new ObjectId(customerId) },
      { 
        $push: { notes: note },
        $set: { updatedAt: new Date() }
      }
    );
  }

  static async addInteraction(customerId, interactionData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const interaction = {
      _id: new ObjectId(),
      ...interactionData,
      createdAt: new Date(),
    };
    
    return await db.collection('customers').updateOne(
      { _id: new ObjectId(customerId) },
      { 
        $push: { interactions: interaction },
        $set: { updatedAt: new Date() }
      }
    );
  }

  static async delete(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection('customers').deleteOne({ _id: new ObjectId(id) });
  }

  static async getStats(filter = {}) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const pipeline = [
      { $match: filter },
      {
        $group: {
          _id: null,
          totalCustomers: { $sum: 1 },
          activeCustomers: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          prospects: {
            $sum: { $cond: [{ $eq: ['$status', 'prospect'] }, 1, 0] }
          },
          customers: {
            $sum: { $cond: [{ $eq: ['$status', 'customer'] }, 1, 0] }
          },
        }
      }
    ];
    
    const result = await db.collection('customers').aggregate(pipeline).toArray();
    return result[0] || {};
  }
}
