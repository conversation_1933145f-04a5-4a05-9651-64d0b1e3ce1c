// Invitation model schema and operations
import clientPromise from '../mongodb';
import { ObjectId } from 'mongodb';
import { v4 as uuidv4 } from 'uuid';

const invitationsCollection = 'tourai.main.dim.users.invitations';
const usersCollection = 'tourai.main.dim.users';
export const InvitationSchema = {
  _id: ObjectId,
  email: String,
  code: String, // unique invitation code
  role: String, // 'admin', 'user'
  status: String, // 'pending', 'used', 'expired', 'cancelled'
  invitedBy: ObjectId, // reference to User who sent invitation
  usedBy: ObjectId, // reference to User who used invitation
  message: String, // optional message from inviter
  expiresAt: Date,
  usedAt: Date,
  createdAt: Date,
  updatedAt: Date,
};

export class InvitationModel {
  static async findById(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection(invitationsCollection).findOne({ _id: new ObjectId(id) });
  }

  static async findByCode(code) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection(invitationsCollection).findOne({ code });
  }

  static async findByEmail(email) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection(invitationsCollection).findOne({ 
      email,
      status: 'pending',
      expiresAt: { $gt: new Date() }
    });
  }

  static async create(invitationData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    // Generate unique invitation code
    const code = uuidv4().replace(/-/g, '').substring(0, 12).toUpperCase();
    
    // Set expiration date (7 days from now by default)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);
    
    const invitation = {
      ...invitationData,
      code,
      status: 'pending',
      expiresAt: invitationData.expiresAt || expiresAt,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const result = await db.collection(invitationsCollection).insertOne(invitation);
    return { ...invitation, _id: result.insertedId };
  }

  static async update(id, updateData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const result = await db.collection(invitationsCollection).updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          ...updateData, 
          updatedAt: new Date() 
        } 
      }
    );
    
    return result;
  }

  static async markAsUsed(code, userId) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection(invitationsCollection).updateOne(
      { code, status: 'pending' },
      { 
        $set: { 
          status: 'used',
          usedBy: new ObjectId(userId),
          usedAt: new Date(),
          updatedAt: new Date()
        } 
      }
    );
  }

  static async cancel(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection(invitationsCollection).updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          status: 'cancelled',
          updatedAt: new Date()
        } 
      }
    );
  }

  static async findAll(filter = {}, options = {}) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
    const skip = (page - 1) * limit;
    
    // Build aggregation pipeline for populated data
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: usersCollection,
          localField: 'invitedBy',
          foreignField: '_id',
          as: 'inviter'
        }
      },
      {
        $lookup: {
          from: usersCollection,
          localField: 'usedBy',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: { path: '$inviter', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const invitations = await db.collection(invitationsCollection).aggregate(pipeline).toArray();
    const total = await db.collection(invitationsCollection).countDocuments(filter);
    
    return {
      invitations,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  static async delete(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection(invitationsCollection).deleteOne({ _id: new ObjectId(id) });
  }

  static async expireOldInvitations() {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection(invitationsCollection).updateMany(
      { 
        status: 'pending',
        expiresAt: { $lt: new Date() }
      },
      { 
        $set: { 
          status: 'expired',
          updatedAt: new Date()
        } 
      }
    );
  }

  static async getStats(filter = {}) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const pipeline = [
      { $match: filter },
      {
        $group: {
          _id: null,
          totalInvitations: { $sum: 1 },
          pendingInvitations: {
            $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] }
          },
          usedInvitations: {
            $sum: { $cond: [{ $eq: ['$status', 'used'] }, 1, 0] }
          },
          expiredInvitations: {
            $sum: { $cond: [{ $eq: ['$status', 'expired'] }, 1, 0] }
          },
          cancelledInvitations: {
            $sum: { $cond: [{ $eq: ['$status', 'cancelled'] }, 1, 0] }
          },
        }
      }
    ];
    
    const result = await db.collection(invitationsCollection).aggregate(pipeline).toArray();
    return result[0] || {};
  }

  static async resend(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    // Extend expiration date by 7 days
    const newExpiresAt = new Date();
    newExpiresAt.setDate(newExpiresAt.getDate() + 7);
    
    return await db.collection(invitationsCollection).updateOne(
      { _id: new ObjectId(id), status: 'pending' },
      { 
        $set: { 
          expiresAt: newExpiresAt,
          updatedAt: new Date()
        } 
      }
    );
  }
}
