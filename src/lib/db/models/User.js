// User model schema and operations
import clientPromise from '../mongodb';
import { ObjectId } from 'mongodb';

const usersCollection = 'tourai.main.dim.users';

export const UserSchema = {
  _id: ObjectId,
  name: String,
  email: String, // unique
  password: String, // hashed, optional for OAuth users
  image: String, // profile image URL
  role: String, // 'admin', 'user'
  provider: String, // 'google', 'credentials'
  isActive: Boolean,
  onboardingCompleted: Boolean,
  preferences: {
    theme: String, // 'light', 'dark', 'system'
    notifications: {
      email: <PERSON>olean,
      push: Boolean,
      sales: <PERSON>olean,
      reports: <PERSON><PERSON><PERSON>,
    },
    dashboard: {
      layout: String,
      widgets: Array,
    },
  },
  profile: {
    phone: String,
    department: String,
    position: String,
    bio: String,
    timezone: String,
  },
  createdAt: Date,
  updatedAt: Date,
  lastLoginAt: Date,
};
export class UserModel {
  static async findById(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection(usersCollection).findOne({ _id: new ObjectId(id) });
  }

  static async findByEmail(email) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection(usersCollection).findOne({ email });
  }

  static async create(userData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const user = {
      ...userData,
      isActive: true,
      onboardingCompleted: false,
      preferences: {
        theme: 'system',
        notifications: {
          email: true,
          push: true,
          sales: true,
          reports: true,
        },
        dashboard: {
          layout: 'default',
          widgets: [],
        },
      },
      profile: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const result = await db.collection(usersCollection).insertOne(user);
    return { ...user, _id: result.insertedId };
  }

  static async update(id, updateData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const result = await db.collection(usersCollection).updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          ...updateData, 
          updatedAt: new Date() 
        } 
      }
    );
    
    return result;
  }

  static async updateLastLogin(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    await db.collection(usersCollection).updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          lastLoginAt: new Date(),
          updatedAt: new Date()
        } 
      }
    );
  }

  static async findAll(filter = {}, options = {}) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const { page = 1, limit = 10, sort = { createdAt: -1 } } = options;
    const skip = (page - 1) * limit;
    
    const users = await db.collection(usersCollection)
      .find(filter, { projection: { password: 0 } })
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray();
    
    const total = await db.collection(usersCollection).countDocuments(filter);
    
    return {
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  static async delete(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection(usersCollection).deleteOne({ _id: new ObjectId(id) });
  }
}
