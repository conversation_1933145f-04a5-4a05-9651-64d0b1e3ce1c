// Product model schema and operations
import clientPromise from '../mongodb';
import { ObjectId } from 'mongodb';

const usersCollection = 'tourai.main.dim.users';
export const ProductSchema = {
  _id: ObjectId,
  name: String,
  description: String,
  sku: String, // unique product identifier
  category: String,
  price: Number,
  currency: String,
  cost: Number, // cost to produce/acquire
  margin: Number, // profit margin percentage
  status: String, // 'active', 'inactive', 'discontinued'
  inventory: {
    quantity: Number,
    minStock: Number,
    maxStock: Number,
    unit: String, // 'piece', 'kg', 'liter', etc.
  },
  specifications: Object, // flexible product specs
  images: [
    {
      url: String,
      alt: String,
      isPrimary: Boolean,
    }
  ],
  tags: [String],
  createdBy: ObjectId, // reference to User
  updatedBy: ObjectId, // reference to User
  createdAt: Date,
  updatedAt: Date,
};

export class ProductModel {
  static async findById(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection('products').findOne({ _id: new ObjectId(id) });
  }

  static async findBySku(sku) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    return await db.collection('products').findOne({ sku });
  }

  static async create(productData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const product = {
      ...productData,
      status: productData.status || 'active',
      currency: productData.currency || 'USD',
      inventory: {
        quantity: 0,
        minStock: 0,
        maxStock: 1000,
        unit: 'piece',
        ...productData.inventory,
      },
      specifications: productData.specifications || {},
      images: productData.images || [],
      tags: productData.tags || [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const result = await db.collection('products').insertOne(product);
    return { ...product, _id: result.insertedId };
  }

  static async update(id, updateData) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const result = await db.collection('products').updateOne(
      { _id: new ObjectId(id) },
      { 
        $set: { 
          ...updateData, 
          updatedAt: new Date() 
        } 
      }
    );
    
    return result;
  }

  static async findAll(filter = {}, options = {}) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const { page = 1, limit = 10, sort = { createdAt: -1 }, search } = options;
    const skip = (page - 1) * limit;
    
    // Add search functionality
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Build aggregation pipeline for populated data
    const pipeline = [
      { $match: filter },
      {
        $lookup: {
          from: usersCollection,
          localField: 'createdBy',
          foreignField: '_id',
          as: 'creator'
        }
      },
      {
        $lookup: {
          from: usersCollection,
          localField: 'updatedBy',
          foreignField: '_id',
          as: 'updater'
        }
      },
      { $unwind: { path: '$creator', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$updater', preserveNullAndEmptyArrays: true } },
      { $sort: sort },
      { $skip: skip },
      { $limit: limit }
    ];
    
    const products = await db.collection('products').aggregate(pipeline).toArray();
    const total = await db.collection('products').countDocuments(filter);
    
    return {
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };
  }

  static async updateInventory(id, quantity, operation = 'set') {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    let updateOperation;
    
    switch (operation) {
      case 'add':
        updateOperation = { $inc: { 'inventory.quantity': quantity } };
        break;
      case 'subtract':
        updateOperation = { $inc: { 'inventory.quantity': -quantity } };
        break;
      case 'set':
      default:
        updateOperation = { $set: { 'inventory.quantity': quantity } };
        break;
    }
    
    updateOperation.$set = { 
      ...updateOperation.$set,
      updatedAt: new Date() 
    };
    
    return await db.collection('products').updateOne(
      { _id: new ObjectId(id) },
      updateOperation
    );
  }

  static async delete(id) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection('products').deleteOne({ _id: new ObjectId(id) });
  }

  static async getCategories() {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection('products').distinct('category');
  }

  static async getLowStockProducts() {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    return await db.collection('products').find({
      $expr: {
        $lte: ['$inventory.quantity', '$inventory.minStock']
      },
      status: 'active'
    }).toArray();
  }

  static async getStats(filter = {}) {
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const pipeline = [
      { $match: filter },
      {
        $group: {
          _id: null,
          totalProducts: { $sum: 1 },
          activeProducts: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          totalValue: { $sum: { $multiply: ['$price', '$inventory.quantity'] } },
          avgPrice: { $avg: '$price' },
          lowStockCount: {
            $sum: {
              $cond: [
                { $lte: ['$inventory.quantity', '$inventory.minStock'] },
                1,
                0
              ]
            }
          },
        }
      }
    ];
    
    const result = await db.collection('products').aggregate(pipeline).toArray();
    return result[0] || {};
  }
}
