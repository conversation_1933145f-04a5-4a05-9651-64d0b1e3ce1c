import { useState, useEffect, useCallback } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { jwtDecode } from 'jwt-decode';

const TOKEN_REFRESH_BUFFER = 15 * 1000; // 15 seconds

export function useTokenManager() {
  const { data: session, status, update } = useSession();
  const [currentToken, setCurrentToken] = useState(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Detect if this is a mobile environment (you can customize this logic)
  const isMobileEnvironment = typeof window !== 'undefined' &&
    (window.navigator.userAgent.includes('Mobile') ||
     window.navigator.userAgent.includes('Android') ||
     window.navigator.userAgent.includes('iPhone'));

  // Get the most current token
  const getCurrentToken = useCallback(() => {
    return currentToken || session?.token || session?.user?.token;
  }, [currentToken, session]);

  // Check if token needs refresh
  const needsRefresh = useCallback((token) => {
    if (!token) return true;
    
    try {
      const decoded = jwtDecode(token);
      const timeUntilExpiry = (decoded.exp * 1000) - Date.now();
      return timeUntilExpiry <= TOKEN_REFRESH_BUFFER;
    } catch (error) {
      console.error('Token decode error:', error);
      return true;
    }
  }, []);

  // Refresh token function - works for both web and mobile
  const refreshToken = useCallback(async () => {
    if (isRefreshing) {
      // If already refreshing, wait for it to complete
      return new Promise((resolve) => {
        const checkRefresh = () => {
          if (!isRefreshing) {
            resolve(getCurrentToken());
          } else {
            setTimeout(checkRefresh, 100);
          }
        };
        checkRefresh();
      });
    }

    setIsRefreshing(true);

    try {
      const refreshTokenValue = session?.refreshToken || session?.user?.refreshToken;

      if (!refreshTokenValue) {
        console.error('No refresh token available, signing out');
        signOut({ callbackUrl: "/auth/signin" });
        throw new Error('No refresh token available');
      }

      console.log('Refreshing token via client-side...');

      // Use the standard refresh endpoint for both web and mobile
      const response = await fetch('/api/auth/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // Add client type header for potential future use
          'X-Client-Type': isMobileEnvironment ? 'mobile' : 'web'
        },
        body: JSON.stringify({ refreshToken: refreshTokenValue }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Token refresh failed:', response.status, errorData);

        // Handle specific error cases
        if (response.status === 401 || errorData.error === 'INVALID_REFRESH_TOKEN') {
          console.log('Refresh token expired or invalid, signing out');
          signOut({ callbackUrl: "/auth/signin" });
          throw new Error('Refresh token expired');
        }

        throw new Error(`Token refresh failed: ${errorData.message || 'Unknown error'}`);
      }

      const { token: newToken, message } = await response.json();
      console.log('Token refreshed successfully via client-side:', message);

      // Update current token
      setCurrentToken(newToken);

      // Update session with new token (for web applications)
      if (session && update) {
        try {
          await update({
            ...session,
            token: newToken,
            user: {
              ...session?.user,
              token: newToken
            }
          });
          console.log('Session updated with new token');
        } catch (updateError) {
          console.warn('Failed to update session, but token refresh succeeded:', updateError);
        }
      }

      return newToken;
    } catch (error) {
      console.error('Token refresh error:', error);

      // Only sign out if it's an authentication error
      if (error.message.includes('expired') || error.message.includes('invalid')) {
        signOut({ callbackUrl: "/auth/signin" });
      }

      throw error;
    } finally {
      setIsRefreshing(false);
    }
  }, [session, isRefreshing, getCurrentToken, update, isMobileEnvironment]);

  // Get valid token (refresh if needed)
  const getValidToken = useCallback(async () => {
    const token = getCurrentToken();
    
    if (!token || needsRefresh(token)) {
      return await refreshToken();
    }
    
    return token;
  }, [getCurrentToken, needsRefresh, refreshToken]);

  // Enhanced fetch with automatic token management
  const authenticatedFetch = useCallback(async (url, options = {}) => {
    try {
      const token = await getValidToken();

      if (!token) {
        throw new Error('No valid token available');
      }

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'X-Client-Type': isMobileEnvironment ? 'mobile' : 'web',
        ...options.headers,
      };

      const response = await fetch(url, {
        ...options,
        headers,
      });

      // Handle 401 by attempting one more refresh
      if (response.status === 401 && !options._isRetry) {
        console.log('Received 401, attempting client-side token refresh...');
        try {
          const refreshedToken = await refreshToken();
          if (refreshedToken) {
            // Retry the request with new token
            return authenticatedFetch(url, {
              ...options,
              _isRetry: true,
              headers: {
                ...options.headers,
                'Authorization': `Bearer ${refreshedToken}`,
                'X-Client-Type': isMobileEnvironment ? 'mobile' : 'web',
              }
            });
          }
        } catch (refreshError) {
          console.error('Token refresh failed during 401 retry:', refreshError);
          // Don't throw here, let the original 401 response be returned
        }
      }

      return response;
    } catch (error) {
      console.error('Authenticated fetch error:', error);
      throw error;
    }
  }, [getValidToken, refreshToken, isMobileEnvironment]);

  // Initialize current token from session
  useEffect(() => {
    if (session?.token && !currentToken) {
      setCurrentToken(session.token);
    }
  }, [session, currentToken]);

  return {
    currentToken: getCurrentToken(),
    getValidToken,
    authenticatedFetch,
    isRefreshing,
    needsRefresh: needsRefresh(getCurrentToken())
  };
}
