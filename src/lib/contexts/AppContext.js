import { createContext, useContext, useState, useEffect } from 'react';
import { useApiClient } from '../fnx/fnx.apiClient';
import { useRouter } from 'next/router';
import { useAuth } from './AuthContext';
import { jwtDecode } from "jwt-decode";

const AppContext = createContext();

export function AppProvider({ children }) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [currentWorkspace, setCurrentWorkspaceState] = useState(null);
  const [workspaces, setWorkspaces] = useState([]);
  const [isLoadingWorkspaces, setIsLoadingWorkspaces] = useState(false);
  const apiClient = useApiClient();
  const router = useRouter();
  const { getToken } = useAuth();

  // Cache constants
  const WORKSPACES_CACHE_EXPIRY = 5 * 60 * 1000; // 5 dakika (milisaniye)

  const formatWorkspaces = (workspaces) => {
    return workspaces?.map(workspace => ({
      _id: workspace._id,
      id: workspace._id,
      name: workspace.name,
      sector: workspace.sector,
      description: workspace.description || '',
      organizationCount: workspace.organizationCount || 0,
      openPositions: workspace.openPositions || 0,
      logo: workspace.logo || `https://ui-avatars.com/api/?name=${encodeURIComponent(workspace.name)}&background=6366f1&color=fff`
    })) || [];
  };

  const listWorkspaces = async (forceRefresh = false) => {
    setIsLoadingWorkspaces(true);
    try {
      const token = await getToken();
      if (!token) {
        console.error('listWorkspaces - No token found');
        return [];
      }

      const decodedToken = jwtDecode(token);
      const { id: userId, clientId } = decodedToken;
      const CACHE_KEY = `workspaces_list_${userId}_${clientId}`;

      // Check cache if not forcing refresh
      if (!forceRefresh) {
        const cachedData = localStorage.getItem(CACHE_KEY);
        if (cachedData) {
          try {
            const { 
              workspaces: cachedWorkspaces, 
              timestamp, 
              userId: cachedUserId, 
              clientId: cachedClientId 
            } = JSON.parse(cachedData);

            const isExpired = Date.now() - timestamp > WORKSPACES_CACHE_EXPIRY;
            // Return cached data if valid
            if (!isExpired && 
                userId === cachedUserId && 
                clientId === cachedClientId && 
                Array.isArray(cachedWorkspaces)) {
              const formattedWorkspaces = formatWorkspaces(cachedWorkspaces);
              console.log('cachedWorkspaces', formattedWorkspaces);
              setWorkspaces(formattedWorkspaces);
              return formattedWorkspaces;
            }
          } catch (error) {
            console.error('Error parsing cached workspaces:', error);
          }
        }
      }
      // Fetch from API if cache miss or force refresh
      const response = await apiClient.get('/api/workspaces');
      const formattedWorkspaces = Array.isArray(response) ? formatWorkspaces(response) : [];
      
      // Update state
      setWorkspaces(formattedWorkspaces);

      // Update cache
      const currentTimestamp = Date.now();
      const cacheData = {
        workspaces: response,
        userId,
        clientId,
        timestamp: currentTimestamp,
        createdAt: new Date(currentTimestamp).toISOString(),
        lastUpdated: new Date(currentTimestamp).toISOString()
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));

      return formattedWorkspaces;
    } catch (error) {
      console.error('Error fetching workspaces:', error);
      return [];
    } finally {
      setIsLoadingWorkspaces(false);
    }
  };

  const toggleSidebar = (value) => {
    setIsCollapsed(value);
    localStorage.setItem('sidebarCollapsed', value);
  };

  const setCurrentWorkspace = async (workspace, fetchMe = true, pushMe = true) => {
    try {
      const token = await getToken();
      if (!token) {
        console.error('No token found');
        return;
      }

      const decodedToken = jwtDecode(token);
      const { id: userId, clientId } = decodedToken;
      const CACHE_KEY = `workspace_selection_${userId}_${clientId}`;

      if (fetchMe) {
        await apiClient.post('/api/workspaceselect', {
          workspaceId: workspace._id,
          workspaceName: workspace.name,
        });
      }

      setCurrentWorkspaceState({_id: workspace._id, name: workspace.name});

      // Cache'i güncelle
      const currentTimestamp = Date.now();
      const cacheData = {
        workspace: {_id: workspace._id, name: workspace.name},
        userId,
        clientId,
        timestamp: currentTimestamp,
        createdAt: new Date(currentTimestamp).toISOString(),
        lastUpdated: new Date(currentTimestamp).toISOString()
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));

      pushMe && router.push('/app/dashboard');
    } catch (error) {
      console.error('Error setting workspace:', error);
    }
  };

  const getCurrentWorkspace = async () => {
    try {
      const token = await getToken();
      if (!token) {
        console.error('getCurrentWorkspace - No token found');
        return null;
      }

      const decodedToken = jwtDecode(token);
      const { id: userId, clientId } = decodedToken;
      const CACHE_KEY = `workspace_selection_${userId}_${clientId}`;
      const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      // Check cache first
      const cachedData = localStorage.getItem(CACHE_KEY);
      if (cachedData) {
        try {
          const { 
            workspace, 
            timestamp, 
            userId: cachedUserId, 
            clientId: cachedClientId 
          } = JSON.parse(cachedData);

          const isExpired = Date.now() - timestamp > CACHE_EXPIRY;
          
          // Return cached data if valid
          if (!isExpired && 
              userId === cachedUserId && 
              clientId === cachedClientId && 
              workspace) {
            setCurrentWorkspaceState(workspace);
            return workspace;
          }
        } catch (error) {
          console.error('Error parsing cached workspace:', error);
        }
      }

      // If cache miss or expired, fetch from API
      const response = await apiClient.get('/api/workspaceselect');
      
      if (response?.workspace) {
        const currentTimestamp = Date.now();
        
        // Update state
        setCurrentWorkspaceState(response.workspace);
        
        // Update cache
        const cacheData = {
          workspace: response.workspace,
          userId,
          clientId,
          timestamp: currentTimestamp,
          createdAt: new Date(currentTimestamp).toISOString(),
          lastUpdated: new Date(currentTimestamp).toISOString()
        };
        localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));

        return response.workspace;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting current workspace:', error);
      return null;
    }
  };

  useEffect(() => {
    const collapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    setIsCollapsed(collapsed);
    // fetchLastWorkspace();
    // listWorkspaces(); // Initial workspace listesini yükle
  }, []);

  const appState = {
    isCollapsed,
    currentWorkspace,
    workspaces,
    isLoadingWorkspaces,
    toggleSidebar,
    setCurrentWorkspace,
    getCurrentWorkspace,
    listWorkspaces,
  };

  return (
    <AppContext.Provider value={appState}>
      {children}
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
