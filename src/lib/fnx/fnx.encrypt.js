// lib/encryption.ts
import * as crypto from "crypto"

const ALGORITHM = "aes-256-gcm"
const IV_LENGTH = 16 // AES-GCM'de IV 16 byte
const AUTH_TAG_LENGTH = 16 // GCM authentication tag uzunluğu

// Ortam değişkeninden 32 byte'lık anahtar al (64 karakter hex)
const ENCRYPTION_KEY = Buffer.from(process.env.ENCRYPTION_KEY, "hex")

if (ENCRYPTION_KEY.length !== 32) {
  throw new Error("ENCRYPTION_KEY must be 32 bytes (64 hex characters) long")
}

/**
 * Şifreleme: <PERSON><PERSON> ne<PERSON>i (örneğin pgConfig) AES-256-GCM ile şifreler
 * Çıktı: { data: string, iv: string, authTag: string }
 */
export function encryptConfig(config) {
  const iv = crypto.randomBytes(IV_LENGTH)
  const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv)

  let encrypted = cipher.update(JSON.stringify(config), "utf8", "hex")
  encrypted += cipher.final("hex")
  const authTag = cipher.getAuthTag()

  return {
    encryptedData: encrypted,
    iv: iv.toString("hex"),
    authTag: authTag.toString("hex")
  }
}

/**
 * Çözme: Şifrelenmiş veriyi AES-256-GCM ile çözer
 * Girdi: { encryptedData, iv, authTag }
 * Çıktı: Orijinal object (örneğin pgConfig)
 */
export function decryptConfig(encryptedData, iv, authTag) {
  const ivBuffer = Buffer.from(iv, "hex")
  const authTagBuffer = Buffer.from(authTag, "hex")
  const encryptedBuffer = Buffer.from(encryptedData, "hex")

  const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, ivBuffer)
  decipher.setAuthTag(authTagBuffer)

  let decrypted = decipher.update(encryptedBuffer, undefined, "utf8")
  decrypted += decipher.final("utf8")

  return JSON.parse(decrypted)
}
