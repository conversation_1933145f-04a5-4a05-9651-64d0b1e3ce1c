import { useAuth } from '../contexts/AuthContext';

import { jwtDecode } from "jwt-decode";

export const useApiClient = () => {
  const { getToken, refreshToken } = useAuth();

  const fetchWithAuth = async (url, options = {}, fnxOptions = {}) => {
    try {
      let token = await getToken();
      let data;
      // Debug: Token'ı kontrol et
      if (!token) {
        console.error('No token available for request:', url);
        return false;
      }

      // // if token expired; refresh token..
      // const decodedToken = jwtDecode(token);
      // const tokenExp = new Date(decodedToken.exp * 1000);
      // const tokenRefreshBuffer = 15 * 1000; // 15 seconds before token expiration
      // if (tokenExp.getTime() - Date.now() < tokenRefreshBuffer) {
      //   console.log('Refreshing token for request:', url, sessionStorage, );
      //   const newToken = await refreshToken();
      //   if (!newToken) {
      //     console.error('Failed to refresh token for request:', url);
      //     return false;
      //   }
      //   token = newToken;
      // }

      // console.log('Making request to:', url, 'with token length:', token?.length);

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      };

      fnxOptions?.debug && console.log('fetchWithAuth', url, options);
      
      let response = await fetch(url, {
        ...options,
        headers,
      });


      if (!response.ok) {
        // throw new Error(data.message || `HTTP error! status: ${response.status}`);
        // If we get a 401, try to refresh the token and retry
        if (response.status === 401) {
          try {
            // Refresh the token
            const newToken = await refreshToken();

            // Update headers with new token
            const newHeaders = {
              ...headers,
              'Authorization': `Bearer ${newToken}`,
            };

            // Retry the request with new token
            response = await fetch(url, { ...options, headers: newHeaders });
            data = await response.json();
          } catch (refreshError) {
            // If refresh fails, re-throw the original error
            throw new Error('Authentication failed');
          }
        }
        else {
          console.log('error:', data.message || `HTTP error! status: ${response.status}`);
          return false;
        }
      } else {
        data = await response.json();
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      // throw error;
      return false;
    }
  };

  const getFreshToken = async () => {
    try {
      const token = await getToken();
      if (!token) {
        console.error('No token available for refresh');
        return false;
      }
      const decodedToken = jwtDecode(token);
      const tokenExp = new Date(decodedToken.exp * 1000);
      const tokenRefreshBuffer = 15 * 1000; // 15 seconds before token expiration
      if (tokenExp.getTime() - Date.now() < tokenRefreshBuffer) {
        const newToken = await refreshToken();
        if (!newToken) {
          console.error('Failed to refresh token');
          return false;
        }
        return newToken;
      }
      return token;
    } catch (error) {
      console.error('Token refresh failed:', error);
      return false;
    }
  };

  return {
    getFreshToken: () => getFreshToken(),
    get: (url, data, fnxOptions) => fetchWithAuth(url, { method: 'GET' }, fnxOptions),
    post: (url, data, fnxOptions) => fetchWithAuth(url, {
      method: 'POST',
      body: JSON.stringify(data),
    }, fnxOptions),
    put: (url, data, fnxOptions) => fetchWithAuth(url, {
      method: 'PUT',
      body: JSON.stringify(data),
    }, fnxOptions),
    delete: (url, fnxOptions) => fetchWithAuth(url, { method: 'DELETE' }, fnxOptions),
  };
};
