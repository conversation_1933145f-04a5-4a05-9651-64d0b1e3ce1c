const nodemailer = require('nodemailer');

exports.sendVerificationEmail = async ({ email, token, name }) => {
    const verificationLink = `${process.env.NEXT_PUBLIC_APP_URL}/auth/verify?token=${token}`;

    const transporter = nodemailer.createTransport({
        host: process.env.YANDEX_HOST,
        port: process.env.YANDEX_HOST_PORT,
        secure: true,
        auth: {
            user: process.env.YANDEX_EMAIL_ADDRESS,
            pass: process.env.YANDEX_EMAIL_PASSWORD
        }
    });

    await transporter.sendMail({
        from: `"TFLOUU" <${process.env.YANDEX_EMAIL_ADDRESS}>`, // SMTP_USER'ı gönderen olarak kullan
        to: email,
        subject: "Verify Your Email Address",
        html: `
            <h1>Welcome to TFLOUU!</h1>
            <p>Hi ${name},</p>
            <p>Please click the link below to verify your email address:</p>
            <p><a href="${verificationLink}">Verify Email</a></p>
            <p>If you didn't create this account, please ignore this email.</p>
        `
    });
};

exports.sendPasswordResetEmail = async ({ email, resetToken }) => {
    const resetLink = `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password/${resetToken}`;

    const conf = {
        host: process.env.YANDEX_HOST,
        port: process.env.YANDEX_HOST_PORT,
        secure: true,
        auth: {
            user: process.env.YANDEX_EMAIL_ADDRESS,
            pass: process.env.YANDEX_EMAIL_PASSWORD
        }
    };
    // console.log('Email config:', conf   );
    const transporter = nodemailer.createTransport(conf);

    await transporter.sendMail({
        from: `"YODA" <${process.env.YANDEX_EMAIL_ADDRESS}>`, // SMTP_USER'ı gönderen olarak kullan
        to: email,
        subject: "Reset Your Password",
        html: `
            <h1>Password Reset Request</h1>
            <p>Click the link below to reset your password:</p>
            <p><a href="${resetLink}">Reset Password</a></p>
            <p>If you didn't request this, please ignore this email.</p>
            <p>This link will expire in 1 hour.</p>
        `
    });
};

exports.sendWelcomeEmail = async ({ email, name, locale = 'en' }) => {
    const transporter = nodemailer.createTransport({
        host: process.env.YANDEX_HOST,
        port: process.env.YANDEX_HOST_PORT,
        secure: true,
        auth: {
            user: process.env.YANDEX_EMAIL_ADDRESS,
            pass: process.env.YANDEX_EMAIL_PASSWORD
        }
    });

    const content = locale === 'tr' ? {
        subject: "YODA'ya Hoş Geldiniz! 🎉",
        greeting: `Merhaba ${name},`,
        welcome: "YODA'ya katıldığınız için teşekkür ederiz! Hesabınız başarıyla oluşturuldu ve artık büyüyen topluluğumuzun bir parçasısınız.",
        features: {
            goals: {
                title: "🎯 Hedefler Belirleyin",
                desc: "İş hedeflerinizi oluşturun ve takip edin"
            },
            analytics: {
                title: "📊 Analitik",
                desc: "Performansınız hakkında içgörüler edinin"
            },
            team: {
                title: "👥 Takım Çalışması",
                desc: "Ekip üyelerinizle işbirliği yapın"
            },
            notifications: {
                title: "🔔 Bildirimler",
                desc: "Önemli uyarılardan haberdar olun"
            }
        },
        getStarted: "Başlamaya hazır mısınız?",
        dashboardButton: "Panele Git",
        help: "Yardıma mı ihtiyacınız var?",
        helpCenter: "Yardım Merkezi",
        helpText: "sayfamızı ziyaret edin veya destek ekibimizle iletişime geçin.",
        followUs: "Bizi takip edin:",
        footer: {
            company: "YODA",
            emailSent: "Bu e-posta şu adrese gönderildi:"
        }
    } : {
        subject: "Welcome to YODA! 🎉",
        greeting: `Hi ${name},`,
        welcome: "Thank you for joining YODA! We're excited to have you on board. Your account has been successfully created, and you're now part of our growing community.",
        features: {
            goals: {
                title: "🎯 Set Goals",
                desc: "Create and track your business objectives"
            },
            analytics: {
                title: "📊 Analytics",
                desc: "Get insights into your performance"
            },
            team: {
                title: "👥 Team Work",
                desc: "Collaborate with your team members"
            },
            notifications: {
                title: "🔔 Notifications",
                desc: "Stay updated with important alerts"
            }
        },
        getStarted: "Ready to get started?",
        dashboardButton: "Go to Dashboard",
        help: "Need help?",
        helpCenter: "Help Center",
        helpText: "or contact our support team.",
        followUs: "Follow us on:",
        footer: {
            company: "YODA Inc. ",
            emailSent: "This email was sent to"
        }
    };

    await transporter.sendMail({
        from: `"YODA" <${process.env.YANDEX_EMAIL_ADDRESS}>`,
        to: email,
        subject: content.subject,
        html: `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${content.subject}</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 0;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header {
                    background: #6366f1;
                    color: white;
                    padding: 30px;
                    text-align: center;
                    border-radius: 8px 8px 0 0;
                }
                .content {
                    background: white;
                    padding: 30px;
                    border: 1px solid #e5e7eb;
                    border-radius: 0 0 8px 8px;
                }
                .button {
                    display: inline-block;
                    background: #6366f1;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 6px;
                    margin: 20px 0;
                }
                .features {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 20px;
                    margin: 30px 0;
                }
                .feature {
                    text-align: center;
                    padding: 20px;
                    background: #f9fafb;
                    border-radius: 8px;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    color: #6b7280;
                    font-size: 14px;
                }
                .social-links {
                    margin: 20px 0;
                }
                .social-links a {
                    margin: 0 10px;
                    color: #6366f1;
                    text-decoration: none;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>${content.subject}</h1>
                </div>
                <div class="content">
                    <h2>${content.greeting}</h2>
                    <p>${content.welcome}</p>
                    
                    <div class="features">
                        <div class="feature">
                            <h3>${content.features.goals.title}</h3>
                            <p>${content.features.goals.desc}</p>
                        </div>
                        <div class="feature">
                            <h3>${content.features.analytics.title}</h3>
                            <p>${content.features.analytics.desc}</p>
                        </div>
                        <div class="feature">
                            <h3>${content.features.team.title}</h3>
                            <p>${content.features.team.desc}</p>
                        </div>
                        <div class="feature">
                            <h3>${content.features.notifications.title}</h3>
                            <p>${content.features.notifications.desc}</p>
                        </div>
                    </div>

                    <p>${content.getStarted}</p>
                    <a href="${process.env.NEXT_PUBLIC_APP_URL}/app/dashboard" class="button">${content.dashboardButton}</a>

                    <p>${content.help} Check out our <a href="${process.env.NEXT_PUBLIC_APP_URL}/help" style="color: #6366f1;">${content.helpCenter}</a> ${content.helpText}</p>

                    <div class="social-links">
                        ${content.followUs}
                        <a href="https://twitter.com/tflouu">Twitter</a> |
                        <a href="https://linkedin.com/company/tflouu">LinkedIn</a>
                    </div>

                    <div class="footer">
                        <p>${content.footer.company}</p>
                        <p>${content.footer.emailSent} ${email}</p>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `
    });
};
