const {encryptConfig, decryptConfig} = require('./fnx.encrypt');
// <PERSON><PERSON><PERSON> kullanım
// import { encryptConfig, decryptConfig } from './fnx.encrypt.js';

(async () => {
    
    const pgConfig = {
        host: '************',
        port: 5432,
        database: 'yoda',
        user: 'yoda_user',
        password: 'Yoda2026!!',
        ssl: true,
    };
    console.log('Original:', pgConfig);
    const encrypted = encryptConfig(pgConfig);
    console.log('Encrypted:', encrypted);
    const decryptedConfig = decryptConfig(
        encrypted.encryptedData,
        encrypted.iv,
        encrypted.authTag
    );
    console.log('Decrypted:', decryptedConfig);
})();

// // Şifrele
// const encrypted = encryptConfig(pgConfig);
// console.log('Encrypted:', encrypted);
// // { encryptedData: 'a1b2c3...', iv: 'd4e5f6...', authTag: 'g7h8i9...' }

// // Sakla (örneğin MongoDB'ye)
// await db.collection('customers').insertOne({
//   customerId: 'cust_123',
//   pgConfig: encrypted, // şifrelenmiş hali
// });

// // Daha sonra: Çöz
// const saved = await db.collection('customers').findOne({ customerId: 'cust_123' });
// const decryptedConfig = decryptConfig(
//   saved.pgConfig.encryptedData,
//   saved.pgConfig.iv,
//   saved.pgConfig.authTag
// );

// console.log(decryptedConfig);
// { host: '...', password: 's3cr3t!', ... } → orijinal