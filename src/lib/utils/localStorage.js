// Local Storage utilities for hotel list preferences

const STORAGE_KEYS = {
  HOTEL_FILTERS: 'hotel_list_filters',
  HOTEL_VIEW_MODE: 'hotel_list_view_mode',
  HOTEL_PREFERENCES: 'hotel_list_preferences'
};

// Default values
const DEFAULT_FILTERS = {
  category: '',
  region: '',
  subregion: '',
  listed: 'TRUE', // Default to website listed = TRUE
  status: '',
  sortBy: 'TESIS_ADI',
  sortOrder: 'asc'
};

const DEFAULT_VIEW_MODE = 'card'; // 'card' or 'table'

const DEFAULT_PREFERENCES = {
  filters: DEFAULT_FILTERS,
  viewMode: DEFAULT_VIEW_MODE,
  showFilters: false
};

// Helper function to safely parse JSON from localStorage
const safeParseJSON = (value, defaultValue) => {
  try {
    return value ? JSON.parse(value) : defaultValue;
  } catch (error) {
    console.warn('Error parsing localStorage value:', error);
    return defaultValue;
  }
};

// Helper function to safely stringify and save to localStorage
const safeSaveJSON = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.warn('Error saving to localStorage:', error);
    return false;
  }
};

// Get hotel list filters from localStorage
export const getStoredFilters = () => {
  if (typeof window === 'undefined') return DEFAULT_FILTERS;
  
  const stored = localStorage.getItem(STORAGE_KEYS.HOTEL_FILTERS);
  return safeParseJSON(stored, DEFAULT_FILTERS);
};

// Save hotel list filters to localStorage
export const saveFilters = (filters) => {
  if (typeof window === 'undefined') return false;
  
  return safeSaveJSON(STORAGE_KEYS.HOTEL_FILTERS, filters);
};

// Get view mode from localStorage
export const getStoredViewMode = () => {
  if (typeof window === 'undefined') return DEFAULT_VIEW_MODE;
  
  const stored = localStorage.getItem(STORAGE_KEYS.HOTEL_VIEW_MODE);
  return stored || DEFAULT_VIEW_MODE;
};

// Save view mode to localStorage
export const saveViewMode = (viewMode) => {
  if (typeof window === 'undefined') return false;
  
  try {
    localStorage.setItem(STORAGE_KEYS.HOTEL_VIEW_MODE, viewMode);
    return true;
  } catch (error) {
    console.warn('Error saving view mode:', error);
    return false;
  }
};

// Get all preferences from localStorage
export const getStoredPreferences = () => {
  if (typeof window === 'undefined') return DEFAULT_PREFERENCES;
  
  const stored = localStorage.getItem(STORAGE_KEYS.HOTEL_PREFERENCES);
  const preferences = safeParseJSON(stored, DEFAULT_PREFERENCES);
  
  // Ensure all required properties exist
  return {
    filters: { ...DEFAULT_FILTERS, ...preferences.filters },
    viewMode: preferences.viewMode || DEFAULT_VIEW_MODE,
    showFilters: preferences.showFilters || false
  };
};

// Save all preferences to localStorage
export const savePreferences = (preferences) => {
  if (typeof window === 'undefined') return false;
  
  const toSave = {
    filters: { ...DEFAULT_FILTERS, ...preferences.filters },
    viewMode: preferences.viewMode || DEFAULT_VIEW_MODE,
    showFilters: preferences.showFilters || false
  };
  
  return safeSaveJSON(STORAGE_KEYS.HOTEL_PREFERENCES, toSave);
};

// Clear all hotel list preferences
export const clearAllPreferences = () => {
  if (typeof window === 'undefined') return false;
  
  try {
    localStorage.removeItem(STORAGE_KEYS.HOTEL_FILTERS);
    localStorage.removeItem(STORAGE_KEYS.HOTEL_VIEW_MODE);
    localStorage.removeItem(STORAGE_KEYS.HOTEL_PREFERENCES);
    return true;
  } catch (error) {
    console.warn('Error clearing preferences:', error);
    return false;
  }
};

// Reset preferences to defaults
export const resetToDefaults = () => {
  if (typeof window === 'undefined') return DEFAULT_PREFERENCES;
  
  savePreferences(DEFAULT_PREFERENCES);
  return DEFAULT_PREFERENCES;
};

// Check if user has any saved preferences
export const hasStoredPreferences = () => {
  if (typeof window === 'undefined') return false;
  
  return !!(
    localStorage.getItem(STORAGE_KEYS.HOTEL_FILTERS) ||
    localStorage.getItem(STORAGE_KEYS.HOTEL_VIEW_MODE) ||
    localStorage.getItem(STORAGE_KEYS.HOTEL_PREFERENCES)
  );
};

export { DEFAULT_FILTERS, DEFAULT_VIEW_MODE, DEFAULT_PREFERENCES };
