import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify, decodeJwt } from 'jose';
import { getToken } from 'next-auth/jwt';

interface AuthResult {
  isValid: boolean;
  payload: any;
  token: string;
}

export default async function middleware(request: NextRequest) {
  // /api/auth/ ve /api/pub/ altındaki route'lar için token kontrolü yapma
  if (request.nextUrl.pathname.startsWith('/api/auth/') ||
      request.nextUrl.pathname.startsWith('/api/pub/')) {
    return NextResponse.next();
  }

  // Önce NextAuth.js token'ını kontrol et
  try {
    const nextAuthToken = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    if (nextAuthToken) {
      return NextResponse.next();
    }
  } catch (error) {
    // NextAuth token error - continue to JWT check
  }

  // NextAuth.js token yoksa, custom JWT token kontrolü yap
  const authResult = await authenticateRequest(request);

  if (!authResult.isValid) {
    return new NextResponse(
      JSON.stringify({
        success: false,
        message: 'Unauthorized',
        requiresLogin: true
      }),
      { status: 401, headers: { 'content-type': 'application/json' } }
    );
  }

  // Token'ı header'a ekle
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('token', authResult.token);

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });

  return response;
}

async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
  const authHeader = request.headers.get('authorization');
  const bearerToken = authHeader ? authHeader.split(' ') : [];
  const token = bearerToken[1];

  if (!token) {
    return {
      isValid: false,
      payload: null,
      token: '',
    };
  }

  const key = process.env.JWT_KEY;
  if (!key) {
    return {
      isValid: false,
      payload: null,
      token: token,
    };
  }

  try {
    const tokenData = await verifyToken(token, key);

    if (tokenData && tokenData.isValid) {
      return {
        isValid: true,
        payload: tokenData.payload,
        token: token,
      };
    } else {
      return {
        isValid: false,
        payload: null,
        token: token,
      };
    }
  } catch (e) {
    // Token verification error
  }

  return {
    isValid: false,
    payload: null,
    token: token,
  };
}

async function verifyToken(token: string, secret: string): Promise<{ isValid: boolean; payload: any }> {
  try {
    // Token'ı decode et (signature kontrolü olmadan)
    const payload = decodeJwt(token);

    // Signature kontrolü için jwtVerify kullan ama expired hatası yakalayalım
    try {
      await jwtVerify(token, new TextEncoder().encode(secret));
    } catch (e) {
      if (e.code === 'ERR_JWT_EXPIRED') {
        // Token expired but signature is valid - accepting for compatibility
      } else {
        // Token signature invalid
        return {
          isValid: false,
          payload: null,
        };
      }
    }

    if (payload) {
      return {
        isValid: true,
        payload: payload,
      };
    }
  } catch (e) {
    // Token decode error
  }

  return {
    isValid: false,
    payload: null,
  };
}

export const config = {
  matcher: ['/api/:path*'],
}
