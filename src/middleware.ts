import { NextRequest, NextResponse } from 'next/server'
import { jwtVerify, decodeJwt } from 'jose';
import { getToken } from 'next-auth/jwt';

interface AuthResult {
  isValid: boolean;
  payload: any;
  token: string;
}

export default async function middleware(request: NextRequest) {
  console.log('🔥 MIDDLEWARE TRIGGERED:', request.nextUrl.pathname, 'Host:', request.nextUrl.host);

  // /api/auth/ ve /api/pub/ altındaki route'lar için token kontrolü yapma
  if (request.nextUrl.pathname.startsWith('/api/auth/') ||
      request.nextUrl.pathname.startsWith('/api/pub/')) {
    console.log('🟢 Skipping auth for:', request.nextUrl.pathname);
    return NextResponse.next();
  }

  // Önce NextAuth.js token'ını kontrol et (sadece localhost için)
  if (request.nextUrl.host === 'localhost:3000') {
    try {
      const nextAuthToken = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET
      });

      console.log('NextAuth token check:', {
        hasToken: !!nextAuthToken,
        host: request.nextUrl.host,
        pathname: request.nextUrl.pathname
      });

      if (nextAuthToken) {
        console.log('🟢 NextAuth session found, allowing request');
        return NextResponse.next();
      }
    } catch (error) {
      console.log('NextAuth token error:', error);
    }
  }

  // NextAuth.js token yoksa, custom JWT token kontrolü yap
  console.log('🔍 Checking custom JWT token...');
  const authResult = await authenticateRequest(request);

  console.log('Auth result:', {
    isValid: authResult.isValid,
    hasPayload: !!authResult.payload,
    hasToken: !!authResult.token
  });

  if (!authResult.isValid) {
    console.log('🔴 Authentication failed for:', request.nextUrl.pathname);
    return new NextResponse(
      JSON.stringify({
        success: false,
        message: 'Unauthorized',
        requiresLogin: true
      }),
      { status: 401, headers: { 'content-type': 'application/json' } }
    );
  }

  console.log('🟢 Custom JWT token valid, adding to headers');
  // Token'ı header'a ekle
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('token', authResult.token);

  console.log('Token added to headers:', authResult.token.substring(0, 20) + '...');
  console.log('Token header set:', requestHeaders.get('token') ? 'YES' : 'NO');

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });

  return response;
}

async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
  const authHeader = request.headers.get('authorization');
  const bearerToken = authHeader ? authHeader.split(' ') : [];
  const token = bearerToken[1];

  console.log('Auth header:', authHeader ? 'present' : 'missing');
  console.log('Token length:', token?.length || 0);

  if (!token) {
    console.log('No token found in request');
    return {
      isValid: false,
      payload: null,
      token: '',
    };
  }

  const key = process.env.JWT_KEY;
  if (!key) {
    console.error('JWT_KEY environment variable is not set');
    return {
      isValid: false,
      payload: null,
      token: token,
    };
  }

  try {
    const tokenData = await verifyToken(token, key);
    // console.log('Token ?, tokenData:', tokenData, token, key);

    if (tokenData && tokenData.isValid) {
      return {
        isValid: true,
        payload: tokenData.payload,
        token: token,
      };
    } else {
      // Token geçersizse refresh token ile yenilemeyi dene
      console.log('Token invalid, attempting refresh for:', request.nextUrl.pathname);

      // Artık hem web hem mobil uygulamalar için client-side token refresh yapacağız
      // Middleware sadece token'ı validate eder, refresh yapmaz
      console.log('Token invalid for:', request.nextUrl.pathname, '- Client should handle token refresh', token);

      return {
        isValid: false,
        payload: null,
        token: token,
      };
    }
  } catch (e) {
    console.log('Token verification error:', request.nextUrl.pathname, e);
  }

  return {
    isValid: false,
    payload: null,
    token: token,
  };
}

async function verifyToken(token: string, secret: string): Promise<{ isValid: boolean; payload: any }> {
  try {
    // console.log('token', token, secret);
    const jwtx = await jwtVerify(token, new TextEncoder().encode(secret));
    const { payload } = jwtx;
    console.log('token payload', payload);

    // Test için token süresini kontrol etmeyi geçici olarak devre dışı bırak
    // if (payload && payload.exp && (Date.now() - payload.exp * 1000) < 0) {
    if (payload) {
      console.log('Token validation: payload exists, accepting token');
      return {
        isValid: true,
        payload: payload,
      };
    }
  } catch (e) {
    // Token geçersiz
    console.log('Token geçersiz', e)
  }

  return {
    isValid: false,
    payload: null,
  };
}

export const config = {
  matcher: ['/api/:path*'],
}
