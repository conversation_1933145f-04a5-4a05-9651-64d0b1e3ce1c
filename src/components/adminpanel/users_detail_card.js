import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';
import { useToast } from '@/lib/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
// import ResetPassword from '@/pages/auth/reset-password/[token]';

const UsersDetailsCard = (props) => {
    const { session, selectedUser } = props;
    const { post, get, put, delete: deleteRequest } = useApiClient();
    const { toast } = useToast();
    const [userData, setuserData] = useState(props.selectedUser || null);
    const [userDataLoading, setuserDataLoading] = useState(false);

    useEffect(() => {
        setuserData(props.selectedUser || []);
        if (props.selectedUser) {
            getUserDetail();
        }
    }, [props.selectedUser]);

    const [userResettingPassword, setUserResettingPassword] = useState(false);

    const resetPassword = async (type = "force") => {
        setUserResettingPassword(true);
        try {
            const res = await fetch(type === "force" ? '/api/auth/reset-password-force' : '/api/auth/forgot-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: userData.email })
            });
            setUserResettingPassword(false);
            if (res.ok) {
                const data = await res.json();
                toast({
                    title: "Success",
                    description: "Password reset link sent",
                });
            } else {
                const errorData = await res.json();
                toast({
                    title: "Error",
                    description: errorData.message || "Failed to send password reset link",
                    variant: "destructive",
                });
            }
        } catch (error) {
            setUserResettingPassword(false);
            console.error('Error sending password reset link:', error);
            toast({
                title: "Error",
                description: "Failed to send password reset link",
                variant: "destructive",
            });
        }
    };
    const getUserDetail = async () => {
        try {
            setuserDataLoading(true);
            const res = await fetch(`/api/tenant/user_detail/${props.selectedUser._id}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            setuserDataLoading(false);
            if (res.ok) {
                const data = await res.json();
                console.log('selected user:', data);
                
                setuserData(data.data || []);
            }
        } catch (error) {
            setuserDataLoading(false);
            console.error('Error fetching customer info:', error);
        }
    };  

    if (userDataLoading) {
        return (
            <div className="p-4 bg-white rounded-lg border border-gray-200">
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium">User Details</h2>
                </div>
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600"></div>
                </div>
            </div>
            );
    }

    const user = userData || props.selectedUser;

    if (!user) {
        return null;
    }

    // Helper function to format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // Get user initials for avatar
    const getUserInitials = (name) => {
        if (!name) return 'U';
        const names = name.split(' ');
        let initials = names[0].substring(0, 1).toUpperCase();
        if (names.length > 1) {
            initials += names[names.length - 1].substring(0, 1).toUpperCase();
        }
        return initials;
    };

    // Get tenant information
    const tenantInfo = user.tenant || (user.tenants && user.tenants.length > 0 ? user.tenants[0] : null);

    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200">
            <div className="flex justify-between items-center pb-4">
                <h2 className="text-lg font-medium">User Details {selectedUser ? ' / ' + selectedUser?.name : ''}</h2>
                <Button 
                    type="button" 
                    onClick={() => {
                        setuserData(null);
                        props.setSelectedUser && props.setSelectedUser(null);
                    }} 
                    variant="outline" 
                    size="sm"
                >
                    x
                </Button>
            </div>

            <div className="space-y-6">
                {/* User Overview Card */}
                <Card>
                    <CardHeader className="pb-4">
                        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                            <Avatar className="h-16 w-16">
                                <AvatarImage src={user.image || ''} alt={user.name} />
                                <AvatarFallback className="bg-primary/10 text-primary">
                                    {getUserInitials(user.name)}
                                </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                                    <div>
                                        <CardTitle className="text-xl">{user.name}</CardTitle>
                                        <CardDescription className="mt-1">{user.email}</CardDescription>
                                    </div>
                                </div>
                                    <div className="flex py-2 gap-1">
                                        <Badge 
                                            className={user.status === 'active' ? 'bg-green-100 text-green-800 hover:bg-green-100' : 'bg-red-100 text-red-800 hover:bg-red-100'}
                                        >
                                            {user.status}
                                        </Badge>
                                        <Badge variant="secondary">
                                            {user.role}
                                        </Badge>
                                    </div>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-sm font-medium text-gray-500">Provider</span>
                                    <span className="text-sm">{user.provider}</span>
                                </div>
                            </div>

                                <div className="flex justify-between">
                                    <span className="text-sm font-medium text-gray-500">Onboarding</span>
                                    <span className="text-sm">
                                        <Badge variant={user.onboardingCompleted ? "default" : "destructive"}>
                                            {user.onboardingCompleted ? "Completed" : "Pending"}
                                        </Badge>
                                    </span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-sm font-medium text-gray-500">Last Updated</span>
                                    <span className="text-sm">{formatDate(user.updatedAt)}</span>
                                </div>
                            <div className="space-y-2">
                                <div className="flex justify-between">
                                    <span className="text-sm font-medium text-gray-500">User ID</span>
                                    <span className="text-sm font-mono text-xs">{user._id}</span>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <div className="space-y-2">
                    <Card>
                        <CardContent>
                            <div className="my-3">
                                <span className="text-sm">Login Options</span>
                            </div>
                            <span className="text-sm">
                                <Badge
                                    className="cursor-pointer"
                                    
                                    onClick={() => {
                                        // TODO://RESET PASSWORD
                                        if (confirm("Are you sure you want to reset password?")) {
                                           !userResettingPassword && resetPassword();
                                        }
                                    }}
                                    variant={userResettingPassword ? "default" : "destructive"}>
                                    {userResettingPassword ? "Resetting Password..." : "Reset Password"} 
                                </Badge>
                            </span>
                        </CardContent>
                    </Card>
                </div>

                {/* Profile Information */}
                {user.profile && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Profile Information</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {user.profile.phone && (
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-500">Phone</span>
                                        <span className="text-sm">{user.profile.phone}</span>
                                    </div>
                                )}
                                {user.preferences && (
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-500">Theme Preference</span>
                                        <span className="text-sm capitalize">{user.preferences.theme}</span>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Positions */}
                {tenantInfo && tenantInfo.positions && tenantInfo.positions.length > 0 && (
                    <PositionsMore positions={tenantInfo.positions} positionsDetailed={user.positions} />
                    
                )}
                {/* Tenant Information */}
                {tenantInfo && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-lg">Tenant Information</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-gray-500">Client ID</span>
                                    <span className="text-sm font-mono text-xs">{tenantInfo.clientId}</span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-gray-500">Role</span>
                                    <span className="text-sm">
                                        <Badge variant="outline">{tenantInfo.roleCode}</Badge>
                                    </span>
                                </div>
                                {tenantInfo.customerName && (
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm font-medium text-gray-500">Customer</span>
                                        <span className="text-sm">{tenantInfo.customerName}</span>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                )}

            </div>
        </div>
    );
};

export default UsersDetailsCard;



const PositionsMore = props => {
    const { positions, positionsDetailed } = props;
    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">Assigned Positions</CardTitle>
                </CardHeader>
                <CardContent>
                    {!positionsDetailed && (
                        <div className="flex flex-wrap gap-2">
                            {Array.isArray(positions) ? positions.map((positionId, index) => (
                                <Badge key={index} variant="secondary">
                                    {positionId}
                                </Badge>
                            )) : (
                                <Badge variant="secondary">
                                    {'no position data'}
                                </Badge>
                            )}
                        </div>
                    )}
                    {positionsDetailed && (
                        <div className="grid grid-cols-1 md:grid-cols-1 gap-4 mt-2">
                            {positionsDetailed.map((position, index) => (
                                <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div className="flex justify-between items-start">
                                        <div>
                                            <h3 className="font-semibold text-lg">{position.position_title}</h3>
                                            <p className="text-sm text-gray-600">{position.department}</p>
                                        </div>
                                        <Badge variant="outline" className="text-xs">
                                            L-G {position.level} - {position.grade}
                                        </Badge>
                                    </div>
                                    
                                    <div className="mt-3 space-y-2">
                                        {position.parent_position_title && (
                                            <div className="flex items-center text-sm">
                                                <span className="text-gray-500 mr-2">Reports to:</span>
                                                <span>{position.parent_position_title}</span>
                                            </div>
                                        )}
                                        
                                        <div className="flex items-center text-sm">
                                            <span className="text-gray-500 mr-2">Roles:</span>
                                        </div>
                                        
                                        {position.roles && position.roles.length > 0 && (
                                            <div className="flex flex-wrap gap-1 mt-2">
                                                {position.roles.map((role, roleIndex) => (
                                                    <Badge key={roleIndex} title={role.description} variant="secondary" className="text-xs">
                                                        {role.roleName}
                                                    </Badge>
                                                ))}
                                            </div>
                                        )}
                                        
                                        <div className="flex items-center text-sm mt-2" title={JSON.stringify(position.permissionDetails)}>
                                            <span className="text-gray-500 mr-2">Permissions:</span>
                                            <span>{position.permissions?.length || 0} assigned</span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                    {/* {JSON.stringify(positionsDetailed, null, 2)} */}
                </CardContent>
            </Card>
        </>
    )
}