import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';
import { useToast } from '@/lib/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';

const PositionsTitlesCard = ({ positionTitles, session, getCustomerUsersInfo }) => {
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [newPositionTitle, setNewPositionTitle] = useState('');
    const [selectedPosition, setSelectedPosition] = useState(null);
    const { post, get, put, delete: deleteRequest } = useApiClient();
    const { toast } = useToast();
    const [positionTitlesData, setPositionTitlesData] = useState([]);
    useEffect(() => {
        if (positionTitles) {
            setPositionTitlesData(positionTitles);
        }
    }, [positionTitles]);
    const handleAddPositionTitle = async () => {
        try {
            const response = await put('/api/tenant/position_titles', { position_title: newPositionTitle });
            if (response && response.data) {
                toast({
                    title: "Success",
                    description: "Position Title created successfully",
                });
                setIsAddModalOpen(false);
                setNewPositionTitle('');
                getCustomerUsersInfo();
            }
        } catch (error) {
            console.error('Error creating position title:', error);
            toast({
                title: "Error",
                description: "Failed to create position title",
                variant: "destructive",
            });
        }
    };

    const handleUpdatePositionTitle = async () => {
        try {
            const response = await put(`/api/tenant/position_titles/${selectedPosition._id}`, {
                position_title: selectedPosition.position_title
            });
            if (response && response.data) {
                toast({
                    title: "Success",
                    description: "Position Title updated successfully",
                });
                setIsEditModalOpen(false);
                setSelectedPosition(null);
                getCustomerUsersInfo();
            }
        } catch (error) {
            console.error('Error updating position title:', error);
            toast({
                title: "Error",
                description: "Failed to update position title",
                variant: "destructive",
            });
        }
    };

    const handleDeletePositionTitle = async () => {
        try {
            const response = await deleteRequest(`/api/tenant/position_titles/${selectedPosition._id}`);
            if (response) {
                toast({
                    title: "Success",
                    description: "Position Title deleted successfully",
                });
                setIsEditModalOpen(false);
                setSelectedPosition(null);
                getCustomerUsersInfo();
            }
        } catch (error) {
            console.error('Error deleting position title:', error);
            toast({
                title: "Error",
                description: "Failed to delete position title",
                variant: "destructive",
            });
        }
    };
    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200">
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium">Position Titles</h2>
                    <Button type="button" onClick={() => setIsAddModalOpen(true)}>
                        +
                    </Button>
                </div>
                <div className="mt-6">
                    <ul className="list-disc list-inside">
                        {positionTitlesData.map((title) => (
                            <li 
                                key={title._id} 
                                className="cursor-pointer hover:text-blue-600"
                                onClick={() => {
                                    setSelectedPosition(title);
                                    setIsEditModalOpen(true);
                                }}
                            >
                                {title.position_title}
                            </li>
                        ))}
                    </ul>
                </div>

            {/* Add Position Title Dialog */}
            <div>
                <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle>Add New Position Title</DialogTitle>
                        </DialogHeader>
                        <div className="space-y-4 py-4">
                            <div>
                                <Label className="text-sm font-medium">Position Title</Label>
                                <Input
                                    name="position_title"
                                    value={newPositionTitle}
                                    onChange={(e) => setNewPositionTitle(e.target.value)}
                                    placeholder="Enter position title"
                                    className="mt-1"
                                />
                            </div>
                        </div>
                        <DialogFooter>
                            <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>
                                Cancel
                            </Button>
                            <Button
                                type="button"
                                onClick={handleAddPositionTitle}
                                disabled={!newPositionTitle}
                            >
                                Add Title
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Edit Position Title Dialog */}
            <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>Edit Position Title</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div>
                            <Label className="text-sm font-medium">Position Title</Label>
                            <Input
                                name="position_title"
                                value={selectedPosition?.position_title || ''}
                                onChange={(e) => setSelectedPosition({
                                    ...selectedPosition,
                                    position_title: e.target.value
                                })}
                                placeholder="Enter position title"
                                className="mt-1"
                            />
                        </div>
                    </div>
                    <DialogFooter className="flex justify-between">
                        <div className="flex w-full gap-2 justify-between">
                            <Button
                                type="button"
                                variant="destructive"
                                onClick={handleDeletePositionTitle}
                            >
                                Delete
                            </Button>

                            <div className="flex gap-2">

                                <Button
                                    type="button"
                                    variant="outline"
                                    onClick={() => {
                                        setIsEditModalOpen(false);
                                        setSelectedPosition(null);
                                    }}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="button"
                                    onClick={handleUpdatePositionTitle}
                                    disabled={!selectedPosition?.position_title}
                                >
                                    Save Changes
                                </Button>
                            </div>
                        </div>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}


export default PositionsTitlesCard;
