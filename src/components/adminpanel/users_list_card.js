import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';
import { useToast } from '@/lib/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell } from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogDescription
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
const UsersListCard = (props) => {
    const { session } = props;
    const [loading, setLoading] = useState(true);
    const [updating, setUpdating] = useState(false);
    const [userList, setUserList] = useState(props.userList || []);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);

    // const [customerPositions, setCustomerPositions] = useState(props.customerPositions || null);
    
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const { post, get, put, delete: deleteRequest } = useApiClient();
    const { toast } = useToast();

    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [newUser, setNewUser] = useState({
        name: '',
        email: '',
        phone: ''
    });
    const [positions, setPositions] = useState(props.positions);
    const [selectedPositions, setSelectedPositions] = useState([]);
    const [positionSearchTerm, setPositionSearchTerm] = useState('');
    const [selectedLoginOptions, setSelectedLoginOptions] = useState([]);

    const filteredUsers = userList.filter(user => 
        user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const usersPerPage = 5;
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const indexOfLastUser = currentPage * usersPerPage;
    const indexOfFirstUser = indexOfLastUser - usersPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);

    const getPositionInfo = posId => {
        let resp = Array.isArray(positions) ? positions.find(pos => pos._id === posId) : {};
        resp = resp || {};
        return resp
    };
    const getPositions = async (force=false) => {
        console.log('positions', positions);
        if (!force && positions.length > 0) return;
        try {
            const response = await post('/api/tenant/positions');
            if (response && response.data) {
                setPositions(response.data);
            }
        } catch (error) {
            console.error('Error fetching positions:', error);
            toast({
                title: "Error",
                description: "Failed to fetch positions",
                variant: "destructive",
            });
        }
    };

    useEffect(() => {
        if (props.positions) {
            setPositions(props.positions);
        } else {
            if (props.session?.token && (Array.isArray(positions) && positions.length == 0)) {
                getPositions();
            }
        }
    }, [props.positions]);

    // Handle opening the add modal
    const handleOpenAddModal = () => {
        setIsAddModalOpen(true);
        setNewUser({
            name: '',
            email: '',
            phone: ''
        });
        setSelectedPositions([]);
        getPositions();
    };

    const handleAddUser = async () => {
        try {
            // For demonstration purposes, we'll update the first user in the list
            // In a real application, you would create a new user through an invitation system
            if (userList.length === 0) {
                toast({
                    title: "Error",
                    description: "No users available to update",
                    variant: "destructive",
                });
                return;
            }

            const firstUser = userList[0];
            const userData = {
                action: 'create',
                userId: firstUser._id || firstUser.id,
                name: newUser.name || firstUser.name,
                email: newUser.email || firstUser.email,
                phone: newUser.phone,
                positions: selectedPositions
            };

            const response = await post('/api/tenant/users', userData);
            if (response) {
                toast({
                    title: "Success",
                    description: "User updated successfully",
                });

                // Close the modal and reset the form
                setIsAddModalOpen(false);
                setNewUser({
                    name: '',
                    email: '',
                    phone: ''
                });
                setSelectedPositions([]);

                // Refresh the user list
                getCustomerUsersInfo();
            } else {
                toast({
                    title: "Error",
                    description: "Failed to update user",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error updating user:', error);
            toast({
                title: "Error",
                description: "Failed to update user",
                variant: "destructive",
            });
        }
    };

    // Handle updating a user
    const handleUpdateUser = async () => {
        try {
            if (!editingRole) {
                toast({
                    title: "Error",
                    description: "No user selected for update",
                    variant: "destructive",
                });
                return;
            }

            setUpdating(true);
            
            const userData = {
                userId: editingRole._id || editingRole.id,
                name: newUser.name,
                email: newUser.email,
                phone: newUser.phone,
                positions: selectedPositions
            };

            const response = await put('/api/tenant/users', userData);
            if (response) {
                toast({
                    title: "Success",
                    description: "User updated successfully",
                });

                // Close the modal and reset the form
                setIsEditModalOpen(false);
                setEditingRole(null);
                setNewUser({
                    name: '',
                    email: '',
                    phone: ''
                });
                setSelectedPositions([]);

                // Refresh the user list
                getCustomerUsersInfo();
            } else {
                toast({
                    title: "Error",
                    description: "Failed to update user",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error updating user:', error);
            toast({
                title: "Error",
                description: "Failed to update user: " + (error.message || "Unknown error"),
                variant: "destructive",
            });
        } finally {
            setUpdating(false);
        }
    };

    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const getCustomerUsersInfo = async () => {
        try {
            // console.log('date ', new Date(Date.now()), 'getPermissions');
            const res = await fetch('/api/tenant/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                console.log('users:', data);
                setUserList(data.data || []);
                props.setUserList && props.setUserList(data.data || []);
                toast({
                    title: "Success",
                    description: "UserList fetched successfully",
                });
            }
            setLoading(false);
        } catch (error) {
            console.error('Error fetching customer info:', error);
            setLoading(false);
        }
    };

    useEffect(() => {
        if (props.userList) {
            setUserList(props.userList);
            setLoading(false);
        } else {
            if (props.session?.token && (Array.isArray(userList) && userList.length == 0)) {
                getCustomerUsersInfo();
            }
        }
    }, [props.userList]);

    const [editingRole, setEditingRole] = useState(null);
    
    // Handle opening the edit modal
    const handleOpenEditModal = (user) => {
        setEditingRole(user);
        setIsEditModalOpen(true);
        // Pre-fill the role data
        setNewUser({
            name: user.name || '',
            email: user.email || '',
            phone: user.profile?.phone || ''
        });
        // Pre-select permissions
        // Find the tenant that matches the current session's clientId
        const currentTenant = user.tenants?.find(tenant => tenant.clientId === session?.tenantData?.clientId) || user.tenants?.[0];
        const userPositions = Array.isArray(currentTenant?.positions) ? currentTenant.positions : [];
        setSelectedPositions(userPositions);
        getPositions();
    };

    if (loading) {
        return (
            <div className="p-4 bg-white rounded-lg border border-gray-200">
                <div className="flex justify-between items-center">
                    <h2 className="text-lg font-medium">Users List</h2>
                    <Button type="button" disabled>
                        Refresh
                    </Button>
                </div>
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-600"></div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200">
            <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium cursor-pointer" onClick={() => getCustomerUsersInfo()}>Users List</h2>
                <Button type="button" onClick={() => handleOpenAddModal()}>
                    +
                </Button>
            </div>
            { /* Users List */  }

            <div className="flex justify-between items-center">
                <div className="relative w-full max-w-sm">
                    <Input
                        type="text"
                        placeholder="Search by name or email..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                        className="pl-10"
                    />
                    <svg
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                        />
                    </svg>
                </div>
            </div>

            <div className="space-y-3">
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Department</TableHead>
                            <TableHead>Position</TableHead>
                            <TableHead>Action</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {currentUsers && currentUsers.length > 0 ? (
                            currentUsers.map((user) => (
                                <TableRow key={user.id || user._id}>
                                    <TableCell className="font-medium">{user.name}</TableCell>
                                    <TableCell>{user.email}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded-full text-xs ${
                                            user.isActive 
                                                ? 'bg-green-100 text-green-800' 
                                                : 'bg-red-100 text-red-800'
                                        }`}>
                                            {user.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                    </TableCell>
                                    {/* <TableCell>{user.profile?.department || '-'}</TableCell> */}
                                     <TableCell>{Array.isArray(user.tenant?.positions) ? (user.tenant?.positions?.map(pos => (getPositionInfo(pos))?.department).join(', ') || JSON.stringify(user.tenant?.positions)) : 'n/a'}</TableCell>
                                    <TableCell>{Array.isArray(user.tenant?.positions) ? (user.tenant?.positions?.map(pos => (getPositionInfo(pos))?.position_title).join(', ') || JSON.stringify(user.tenant?.positions)) : 'n/a'}</TableCell>
                                    <TableCell>
                                        <span className="text-blue-500 cursor-pointer text-xs" onClick={() => props.setSelectedUser && props.setSelectedUser(user)}>View</span>
                                        &nbsp;|&nbsp;
                                        <span className="text-blue-500 cursor-pointer text-xs" onClick={() => handleOpenEditModal(user)}>Edit</span>
                                    
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                                    No users found
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Add User Dialog */}
            <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                <DialogContent className="max-w-md">
                    <DialogDescription className="sr-only">
                        Create a new role by entering the role name...
                    </DialogDescription>
                    <DialogHeader>
                        <DialogTitle>Add New User</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="name" className="text-right">
                                Name
                            </Label>
                            <Input
                                id="name"
                                className="col-span-3"
                                value={newUser.name}
                                onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="email" className="text-right">
                                Email
                            </Label>
                            <Input
                                id="email"
                                type="email"
                                className="col-span-3"
                                value={newUser.email}
                                onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="phone" className="text-right">
                                Phone
                            </Label>
                            <Input
                                id="phone"
                                className="col-span-3"
                                value={newUser.phone}
                                onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                            />
                        </div>

                        {/* TODO://LOGIN OPTIONS IN CUSTOMER */}
                        {/* <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="phone" className="text-right">
                                Login Options
                            </Label>
                            <div key={'lo1'} className="flex items-center space-x-2 mb-2">
                                <Checkbox
                                    id={`lo1`}
                                    checked={selectedLoginOptions?.includes('email')}
                                    onCheckedChange={(checked) => {
                                        if (checked) { 
                                            setSelectedLoginOptions([selectedLoginOptions, 'email']);
                                        } else {
                                            setSelectedLoginOptions([selectedLoginOptions.filter(option => option !== 'email')]);
                                        }
                                    }}
                                />
                                <Label
                                    htmlFor={`lo1`}
                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                    {'Email'} 
                                </Label>
                            </div>
                        </div> */}
{/* 
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="phone" className="text-right">
                                Password
                            </Label>
                            <Input
                                id="phone"
                                className="col-span-3"
                                value={newUser.password}
                                onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                            />
                        </div> */}
                        <div className="grid grid-cols-4 items-start gap-4">
                            <Label className="text-right pt-3">
                                Positions
                            </Label>
                            <div className="col-span-3"> 
                                 <div className="mb-3">
                                        <Input
                                            placeholder="Search positions..."
                                            value={positionSearchTerm}
                                            onChange={(e) => setPositionSearchTerm(e.target.value)}
                                            className="w-full"
                                        />
                                    </div>
                                <div className="border rounded-md p-3 max-h-[150px] min-h-[150px]  overflow-y-auto">
                                    {/* Search input for positions */}
                                   
                                    {Array.isArray(positions) && positions.length > 0 ? (
                                        positions
                                            .filter(position =>
                                                position.position_title?.toLowerCase().includes(positionSearchTerm.toLowerCase()) ||
                                                position.level?.toString().includes(positionSearchTerm)
                                            )
                                            .map((position) => (
                                                <div key={position._id} className="flex items-center space-x-2 mb-2">
                                                    <Checkbox
                                                        id={`position-${position._id}`}
                                                        checked={selectedPositions.includes(position._id)}
                                                        onCheckedChange={(checked) => {
                                                            if (checked) {
                                                                setSelectedPositions([...selectedPositions, position._id]);
                                                            } else {
                                                                setSelectedPositions(selectedPositions.filter(id => id !== position._id));
                                                            }
                                                        }}
                                                    />
                                                    <Label
                                                        htmlFor={`position-${position._id}`}
                                                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                                    >
                                                        {position.position_title || 'Unnamed Position'} - Level {position.level}
                                                    </Label>
                                                </div>
                                            ))
                                    ) : (
                                        <p className="text-sm text-gray-500">No positions available</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            onClick={handleAddUser}
                            disabled={!newUser.name || !newUser.email}
                        >
                            Add User
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Edit User Dialog */}
            <Dialog open={isEditModalOpen} onOpenChange={(open) => {
                setIsEditModalOpen(open);
                if (!open) {
                    setEditingRole(null);
                    setNewUser({
                        name: '',
                        email: '',
                        phone: ''
                    });
                    setSelectedPositions([]);
                }
            }}>
                <DialogContent className="max-w-md">
                    <DialogDescription className="sr-only">
                        Edit user information
                    </DialogDescription>
                    <DialogHeader>
                        <DialogTitle>Edit User</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="edit-name" className="text-right">
                                Name
                            </Label>
                            <Input
                                id="edit-name"
                                className="col-span-3"
                                value={newUser.name}
                                onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="edit-email" className="text-right">
                                Email
                            </Label>
                            <Input
                                id="edit-email"
                                type="email"
                                className="col-span-3"
                                value={newUser.email}
                                onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                            />
                        </div>
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="edit-phone" className="text-right">
                                Phone
                            </Label>
                            <Input
                                id="edit-phone"
                                className="col-span-3"
                                value={newUser.phone}
                                onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                            />
                        </div>
                        <div className="grid grid-cols-4 items-start gap-4">
                            <Label className="text-right pt-3">
                                Positions
                            </Label>
                            <div className="col-span-3">
                                 <div className="mb-3">
                                        <Input
                                            placeholder="Search positions..."
                                            value={positionSearchTerm}
                                            onChange={(e) => setPositionSearchTerm(e.target.value)}
                                            className="w-full"
                                        />
                                    </div>
                                <div className="border rounded-md p-3 max-h-[150px] min-h-[150px]  overflow-y-auto">
                                    {Array.isArray(positions) && positions.length > 0 ? (
                                        positions
                                            .filter(position =>
                                                position.position_title?.toLowerCase().includes(positionSearchTerm.toLowerCase()) ||
                                                position.level?.toString().includes(positionSearchTerm)
                                            )
                                            .map((position) => (
                                                <div key={position._id} className="flex items-center space-x-2 mb-2">
                                                    <Checkbox
                                                        id={`edit-position-${position._id}`}
                                                        checked={selectedPositions.includes(position._id)}
                                                        onCheckedChange={(checked) => {
                                                            if (checked) {
                                                                setSelectedPositions([...selectedPositions, position._id]);
                                                            } else {
                                                                setSelectedPositions(selectedPositions.filter(id => id !== position._id));
                                                            }
                                                        }}
                                                    />
                                                    <Label
                                                        htmlFor={`edit-position-${position._id}`}
                                                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                                    >
                                                        {position.position_title || 'Unnamed Position'} - Level {position.level}
                                                    </Label>
                                                </div>
                                            ))
                                    ) : (
                                        <p className="text-sm text-gray-500">No positions available</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button
                            onClick={handleUpdateUser}
                            disabled={!newUser.name || !newUser.email || updating}
                        >
                            {updating ? "Updating..." : "Update User"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default UsersListCard;
