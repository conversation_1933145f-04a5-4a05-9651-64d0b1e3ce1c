import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';
import { useToast } from '@/lib/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogDescription,
} from '@/components/ui/dialog';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';

const RolesListCard = (props) => {
    const { session } = props;
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [editingRole, setEditingRole] = useState(null);
    const { post, get, put, delete: deleteRequest, getFreshToken } = useApiClient();
    const { toast } = useToast();
    const [rolesData, setRolesData] = useState(props.roles || null);

    // State for new role creation
    const [newRole, setNewRole] = useState({
        roleName: '',
        description: '',
        responsibilities: ''
    });
    const [permissionsData, setPermissionsData] = useState([]);
    const [selectedPermissions, setSelectedPermissions] = useState([]);
    const [loadingPermissions, setLoadingPermissions] = useState(false);


    const getRoles = async () => {
        try {
            // const tokenn = await getFreshToken();
            // console.log('token vs tokenn:', session?.token, tokenn);
            const res = await fetch('/api/tenant/roles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                console.log('roles:', data);
                setRolesData(data.data || []);
                props.setRoles && props.setRoles(data.data || []);
            }
        } catch (error) {
            console.error('Error fetching customer info:', error);
        }
    };

    // Fetch permissions when the add modal opens
    const getPermissions = async (force=false) => {
        if (!force && permissionsData.length > 0) return;
        setLoadingPermissions(true);
        try {
            const res = await fetch('/api/tenant/permissions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            if (res.ok) {
                const data = await res.json();
                setPermissionsData(data.data || []);
            } else {
                toast({
                    title: "Error",
                    description: "Failed to fetch permissions",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error fetching permissions:', error);
            toast({
                title: "Error",
                description: "Failed to fetch permissions",
                variant: "destructive",
            });
        } finally {
            setLoadingPermissions(false);
        }
    };

    // Handle opening the add modal
    const handleOpenAddModal = () => {
        setIsAddModalOpen(true);
        setNewRole({
            roleName: '',
            description: '',
            responsibilities: ''
        });
        setSelectedPermissions([]);
        getPermissions();
    };

    // Handle opening the edit modal
    const handleOpenEditModal = (role) => {
        setEditingRole(role);
        setIsEditModalOpen(true);
        // Pre-fill the role data
        setNewRole({
            roleName: role.roleName || '',
            description: role.description || '',
            responsibilities: role.responsibilities || ''
        });
        // Pre-select permissions
        const rolePermissions = role.permissions || [];
        setSelectedPermissions(rolePermissions);
        getPermissions();
    };

    const deleteRole = async () => {
        try {
            if (confirm('Are you sure you want to delete this role?')) {
                const res = await fetch(`/api/tenant/roles`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${session?.token}`
                    },
                    body: JSON.stringify({
                        action: 'delete',
                        roleId: editingRole._id
                    })
                });
                if (res.ok) {
                    const data = await res.json();
                    toast({
                        title: "Success",
                        description: "Role deleted successfully",
                    });
                    setIsEditModalOpen(false);
                    getRoles(); // Refresh roles list
                } else {
                    const errorData = await res.json();
                    toast({
                        title: "Error",
                        description: errorData.message || "Failed to delete role",
                        variant: "destructive",
                    });
                }
            } else {
                toast({
                    title: "Cancelled",
                    description: "Role deletion cancelled",
                });
                return;
            }
        } catch (error) {
            console.error('Error deleting role:', error);
            toast({
                title: "Error",
                description: "Failed to delete role",
                variant: "destructive",
            });
        }
    };
    // Update existing role
    const updateRole = async () => {
        try {
            const res = await fetch(`/api/tenant/roles`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                },
                body: JSON.stringify({
                    action: 'update',
                    roleId: editingRole._id,
                    roleName: newRole.roleName,
                    description: newRole.description,
                    responsibilities: newRole.responsibilities,
                    permissions: selectedPermissions
                })
            });

            if (res.ok) {
                const data = await res.json();
                toast({
                    title: "Success",
                    description: "Role updated successfully",
                });
                setIsEditModalOpen(false);
                getRoles(); // Refresh roles list
            } else {
                const errorData = await res.json();
                toast({
                    title: "Error",
                    description: errorData.message || "Failed to update role",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error updating role:', error);
            toast({
                title: "Error",
                description: "Failed to update role",
                variant: "destructive",
            });
        }
    };

    // Handle role input changes
    const handleRoleChange = (field, value) => {
        setNewRole(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Handle permission selection
    const handlePermissionToggle = (permissionCode) => {
        setSelectedPermissions(prev => {
            if (prev.includes(permissionCode)) {
                return prev.filter(code => code !== permissionCode);
            } else {
                return [...prev, permissionCode];
            }
        });
    };

    // Create new role
    const createRole = async () => {
        try {
            const res = await fetch('/api/tenant/roles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                },
                body: JSON.stringify({
                    action: 'create',
                    roleName: newRole.roleName,
                    description: newRole.description,
                    responsibilities: newRole.responsibilities,
                    permissions: selectedPermissions
                })
            });

            if (res.ok) {
                const data = await res.json();
                toast({
                    title: "Success",
                    description: "Role created successfully",
                });
                setIsAddModalOpen(false);
                getRoles(); // Refresh roles list
            } else {
                const errorData = await res.json();
                toast({
                    title: "Error",
                    description: errorData.message || "Failed to create role",
                    variant: "destructive",
                });
            }
        } catch (error) {
            console.error('Error creating role:', error);
            toast({
                title: "Error",
                description: "Failed to create role",
                variant: "destructive",
            });
        }
    };

    // Render permission groups
    const renderPermissionGroups = () => {
        const groupedPermissions = permissionsData.reduce((acc, permission) => {
            const moduleKey = permission.module || 'unknown';
            if (!acc[moduleKey]) {
                acc[moduleKey] = {
                    module: permission.module,
                    moduleTitle: permission.moduleTitle || permission.module,
                    permissions: []
                };
            }
            acc[moduleKey].permissions.push(permission);
            return acc;
        }, {});

        return Object.values(groupedPermissions).map((module) => (
            <div key={module.module} className="border-b last:border-b-0">
                <div className="bg-gray-50 p-2 font-medium">
                    {module.moduleTitle}
                </div>
                <div className="p-2 space-y-2">
                    {module.permissions.map((permission) => (
                        <div key={permission._id} className="flex items-center space-x-2">
                            <Checkbox
                                id={`permission-${permission.code}`}
                                checked={selectedPermissions.includes(permission.code)}
                                onCheckedChange={() => handlePermissionToggle(permission.code)}
                            />
                            <Label
                                htmlFor={`permission-${permission.code}`}
                                className="text-sm font-normal cursor-pointer flex-1"
                            >
                                <div className="font-medium">{permission.code}</div>
                                <div className="text-gray-500 text-xs">{permission.desc}</div>
                            </Label>
                        </div>
                    ))}
                </div>
            </div>
        ));
    };

    useEffect(() => {
        if (props.session?.token && rolesData == null) {
            getRoles();
        }
    }, [props.session?.token]);
    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200">
            <div className="flex justify-between items-center pb-4">
                <h2 className="text-lg font-medium cursor-pointer" onClick={() => getRoles()}>Roles Titles</h2>
                <Button type="button" onClick={handleOpenAddModal}>
                    +
                </Button>
            </div>
            <span>sadece active roller ve rol arama - silmek icin tanımlı poz kalmamalı / tanimli pozlardan o rol tanimi da silinmeli... - listeye de tanımlaşmış poz adedi...</span>
            {rolesData && rolesData.length > 0 ? (
                <div className="border rounded-lg overflow-hidden">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="font-medium">Role Name</TableHead>
                                <TableHead className="font-medium">Description</TableHead>
                                <TableHead className="font-medium">Status</TableHead>
                                <TableHead className="font-medium">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {rolesData.map((role) => (
                                <TableRow
                                    key={role._id}
                                    className="hover:bg-gray-50 transition-colors"
                                >
                                    <TableCell className="font-medium cursor-pointer"
                                        onClick={() => props.setSelectedRole && props.setSelectedRole(role)}>{role.roleName}</TableCell>
                                    <TableCell className="font-medium cursor-pointer"
                                        onClick={() => props.setSelectedRole && props.setSelectedRole(role)}>{role.description}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${role.status === 'active'
                                                ? 'bg-green-100 text-green-800'
                                                : 'bg-red-100 text-red-800'
                                            }`}>
                                            {role.status}
                                        </span>
                                    </TableCell>
                                    <TableCell>
                                        <span className="text-blue-500 cursor-pointer text-xs" onClick={() => props.setSelectedRole && props.setSelectedRole(role)}>View</span>
                                        &nbsp;|&nbsp;
                                        <span className="text-blue-500 cursor-pointer text-xs" onClick={() => handleOpenEditModal(role)}>Edit</span>
                                    </TableCell>
                                    {/* <TableCell>
                                        <div className="flex flex-wrap gap-1">
                                            {role.permissions && role.permissions.slice(0, 2).map((perm, index) => (
                                                <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                                                    {perm}
                                                </span>
                                            ))}
                                            {role.permissions && role.permissions.length > 2 && (
                                                <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">
                                                    +{role.permissions.length - 2}
                                                </span>
                                            )}
                                        </div>
                                    </TableCell> */}
                                    {/* <TableCell>
                                        {new Date(role.updatedAt).toLocaleDateString('en-US', {
                                            year: 'numeric',
                                            month: 'short',
                                            day: 'numeric'
                                        })}
                                    </TableCell> */}
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            ) : (
                <p className="text-gray-500 text-center py-4">No roles found</p>
            )}

            {/* Add Role Dialog */}
            <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                <DialogContent className="max-w-2xl max-h-[80vh] p-0 flex flex-col" aria-describedby="create-role-description">
                    <DialogDescription className="sr-only">
                        Create a new role by entering the role name...
                    </DialogDescription>
                    <DialogHeader className="border-b p-4">
                        <DialogTitle>Create New Role</DialogTitle>
                    </DialogHeader>
                    <div className="flex-1 overflow-y-auto p-6 pt-0">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="roleName">Role Name</Label>
                                <Input
                                    id="roleName"
                                    value={newRole.roleName}
                                    onChange={(e) => handleRoleChange('roleName', e.target.value)}
                                    placeholder="Enter role name"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">Description</Label>
                                <Input
                                    id="description"
                                    value={newRole.description}
                                    onChange={(e) => handleRoleChange('description', e.target.value)}
                                    placeholder="Enter role description"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="responsibilities">Responsibilities</Label>
                                <Input
                                    id="responsibilities"
                                    value={newRole.responsibilities}
                                    onChange={(e) => handleRoleChange('responsibilities', e.target.value)}
                                    placeholder="Enter role responsibilities"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label onClick={() => getPermissions(true)} className="cursor-pointer">Permissions</Label>
                                {loadingPermissions ? (
                                    <div className="flex items-center justify-center h-32">
                                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                    </div>
                                ) : (
                                    <div className="border rounded-md max-h-60 overflow-y-auto">
                                        {permissionsData && permissionsData.length > 0 ? (
                                            // Group permissions by module
                                            renderPermissionGroups()
                                        ) : (
                                            <div className="p-4 text-center text-gray-500">
                                                No permissions available
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <DialogFooter className="z-10 border-t p-2">
                        <Button variant="outline" onClick={() => setIsAddModalOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={createRole}>
                            Create Role
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Edit Role Dialog */}
            <Dialog open={isEditModalOpen} onOpenChange={(open) => {
                setIsEditModalOpen(open);
                if (!open) setEditingRole(null);
            }}>
                <DialogContent className="max-w-2xl max-h-[80vh] p-0 flex flex-col" aria-describedby="edit-role-description">
                    <DialogDescription className="sr-only">
                        Edit role by modifying the role name...
                    </DialogDescription>
                    <DialogHeader className="border-b p-4">
                        <DialogTitle>Edit Role</DialogTitle>
                    </DialogHeader>
                    <div className="flex-1 overflow-y-auto p-6 pt-0">
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="edit-roleName">Role Name</Label>
                                <Input
                                    id="edit-roleName"
                                    value={newRole.roleName}
                                    onChange={(e) => handleRoleChange('roleName', e.target.value)}
                                    placeholder="Enter role name"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="edit-description">Description</Label>
                                <Input
                                    id="edit-description"
                                    value={newRole.description}
                                    onChange={(e) => handleRoleChange('description', e.target.value)}
                                    placeholder="Enter role description"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="edit-responsibilities">Responsibilities</Label>
                                <Input
                                    id="edit-responsibilities"
                                    value={newRole.responsibilities}
                                    onChange={(e) => handleRoleChange('responsibilities', e.target.value)}
                                    placeholder="Enter role responsibilities"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label onClick={() => getPermissions(true)} className="cursor-pointer">Permissions</Label>
                                {loadingPermissions ? (
                                    <div className="flex items-center justify-center h-32">
                                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                                    </div>
                                ) : (
                                    <div className="border rounded-md max-h-60 overflow-y-auto">
                                        {permissionsData && permissionsData.length > 0 ? (
                                            // Group permissions by module
                                            renderPermissionGroups()
                                        ) : (
                                            <div className="p-4 text-center text-gray-500">
                                                No permissions available
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>

                    <DialogFooter className="z-10 border-t p-2">
                        <div className="flex w-full gap-2 justify-between">
                            <Button className="w-32 bg-red-300 hover:bg-red-600" variant="outline" onClick={() => deleteRole()}>
                                Delete
                            </Button>
                            <div className="flex gap-2">
                                <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={updateRole}>
                                    Update Role
                                </Button>
                            </div>
                        </div>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}

export default RolesListCard;
