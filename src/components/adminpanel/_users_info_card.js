import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter
} from '@/components/ui/dialog';
import {
    UsersIcon, // <-- Use this
    PencilIcon
} from '@heroicons/react/24/outline';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { useAuth } from '@/lib/contexts/AuthContext';

const AccountUsersInfoCard = (props) => {
    const { session, positions, positionTitles, departmentz } = props;
    const { refreshToken, getToken } = useAuth();
    const [customerUsersInfo, setCustomerUsersInfo] = useState(props.userList || null);
    const [loading, setLoading] = useState(true);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [customerPositions, setCustomerPositions] = useState(props.customerPositions || null);
    const [positionSearchTerm, setPositionSearchTerm] = useState('');
    const [selectedPositions, setSelectedPositions] = useState([]);
    const [editUserData, setEditUserData] = useState({
        name: '',
        email: '',
        phone: ''
    });
    
    const [userList, setUserList] = useState(props.userList || []);

    // Helper function to make API calls with automatic token refresh
    const fetchWithAuth = async (url, options = {}) => {
        // Get the current token
        let token = await getToken();
        
        // Add authorization header
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            ...options.headers,
        };
        
        // Make the initial request
        let response = await fetch(url, { ...options, headers });
        
        // If we get a 401, try to refresh the token and retry
        if (response.status === 401) {
            try {
                // Refresh the token
                const newToken = await refreshToken();
                
                // Update headers with new token
                const newHeaders = {
                    ...headers,
                    'Authorization': `Bearer ${newToken}`,
                };
                
                // Retry the request with new token
                response = await fetch(url, { ...options, headers: newHeaders });
            } catch (refreshError) {
                // If refresh fails, re-throw the original error
                throw new Error('Authentication failed');
            }
        }
        
        return response;
    };

    const getCustomerUsersInfo = async () => {
        try {
            const res = await fetchWithAuth('/api/tenant/users', {
                method: 'POST'
            });
            setLoading(false);
            if (res.ok) {
                const data = await res.json();
                console.log('Customer getCustomerUsersInfo :', data);
                setUserList(data.data);
                props.setUserList && props.setUserList(data.data);
            } else {
                console.log('Failed to fetch customer users info', res);
                if (res.status === 401) {
                    // Handle unauthorized error
                    // The fetchWithAuth function should handle token refresh automatically
                    console.log('Unauthorized access - token may have expired');
                } else if (res.status === 403) {
                    console.log('Forbidden access - insufficient permissions');
                } else {
                    console.log(`HTTP error! status: ${res.status}`);
                }
            }
        } catch (error) {
            setLoading(false);
            console.error('Error fetching customer users info:', error);
        }
    };

    useEffect(() => {
        if (props.userList) {
            setUserList(props.userList);
        } else {

        if (props.session?.token && (Array.isArray(userList) && userList.length == 0)) {
            getCustomerUsersInfo();
        }
        }
    }, [props.userList]);

    // useEffect(() => {
    //     if (props.session?.token && (Array.isArray(userList) && userList.length == 0)) {
    //         getCustomerUsersInfo();
    //     }
    // }, [props.session?.token]);
    
    // Initialize selected positions and edit user data when modal opens
    useEffect(() => {
        if (isEditModalOpen && selectedUser) {
            const userPositions = selectedUser.tenantData?.positions || [];
            setSelectedPositions(userPositions.map(pos => pos._id));
            
            // Initialize edit user data
            setEditUserData({
                name: selectedUser.name || '',
                email: selectedUser.email || '',
                phone: selectedUser.profile?.phone || ''
            });
        }
    }, [isEditModalOpen, selectedUser]);
    
    // Filter positions based on search term
    const filteredPositions = customerPositions?.filter(position =>
        position.position_title?.toLowerCase().includes(positionSearchTerm.toLowerCase())
    ) || [];
    
    // Handle position selection
    const handlePositionChange = (positionId) => {
        setSelectedPositions(prev => {
            if (prev.includes(positionId)) {
                return prev.filter(id => id !== positionId);
            } else {
                return [...prev, positionId];
            }
        });
    };
    
    // Handle edit user data changes
    const handleEditUserChange = (e) => {
        const { name, value } = e.target;
        setEditUserData(prev => ({
            ...prev,
            [name]: value
        }));
    };
    const usersPerPage = 5;
    useEffect(() => {
        // props.customerUsersInfo && setCustomerUsersInfo(props.customerUsersInfo);
        props.positions && setCustomerPositions(props.positions);
        props.userList && setLoading(false);
    }, [props.userList, props.positions]);



    useEffect(() => {
        // Reset to first page when customerUsersInfo changes
    }, [isEditModalOpen]);

    const getUsersArray = () => {
        // console.log('Customer Users Info Raw:', customerUsersInfo); 
        if (!userList) return [];
        if (Array.isArray(userList)) {
            return userList;
        }
        return [];
    };

    // Filter users based on search term
    const allUsers = getUsersArray();
    const filteredUsers = allUsers.filter(user => 
        user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase())
    );
// console.log('Filtered Users:', allUsers, filteredUsers);
    // Pagination logic
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const indexOfLastUser = currentPage * usersPerPage;
    const indexOfFirstUser = indexOfLastUser - usersPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);

    const handleEditUser = (user) => {
        setSelectedUser(user);
        setIsEditModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsEditModalOpen(false);
        setSelectedUser(null);
    };

    const handleSaveEdit = async () => {
        try {
            // Use the correct user ID (keep _id for database operations)
            const userId = selectedUser._id || selectedUser.id;
            
            // Update user information
            const response = await fetchWithAuth('/api/tenant/users', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    userId: userId,
                    name: editUserData.name,
                    email: editUserData.email,
                    phone: editUserData.phone,
                    positions: selectedPositions
                })
            });

            if (response.ok) {
                // Close the modal
                handleCloseModal();
                // Refresh user info
                props.getCustomerUsersInfo();
            } else {
                console.error('Failed to update user info');
                // Handle different error cases
                if (response.status === 401) {
                    console.log('Unauthorized access - token may have expired');
                } else if (response.status === 403) {
                    console.log('Forbidden access - insufficient permissions');
                } else {
                    console.log(`HTTP error! status: ${response.status}`);
                }
            }
        } catch (error) {
            console.error('Error updating user info:', error);
        }
    };

    const handleSearchChange = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
    };

    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <>

            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Users List</CardTitle>
                    
                </CardHeader>
                <CardContent className="space-y-4">
                    {currentUsers && currentUsers.length > 0 && (
                        <div className="space-y-4">
                            {/* Search Input */}
                            <div className="flex justify-between items-center">
                                <div className="relative w-full max-w-sm">
                                    <Input
                                        type="text"
                                        placeholder="Search by name or email..."
                                        value={searchTerm}
                                        onChange={handleSearchChange}
                                        className="pl-10"
                                    />
                                    <svg
                                        className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                        />
                                    </svg>
                                </div>
                            </div>

                            {/* Users List */}
                            <div className="space-y-3">
                                {currentUsers.length > 0 ? (
                                    currentUsers.map((user) => (
                                        <div 
                                            key={user._id + '_' + user.email} 
                                            className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-150 cursor-pointer"
                                            onClick={() => handleEditUser(user)}
                                        >
                                            <div className="flex items-start space-x-4">
                                                <div className="flex-1">
                                                    <h3 className="text-lg font-medium text-gray-900">{user.name}</h3>
                                                    <p className="text-sm text-gray-500">{user.email}</p>

                                                    <div className="text-sm text-gray-500 mt-4">
                                                        <span className="text-gray-700 text-xs">Positions:</span><br />
                                                        {
                                                            //TODO: position code ları yanına tanımlarını getir apiden.
                                                        }
                                                        {(Array.isArray(user.tenantData?.positions) ? (user.tenantData.positions.map(pos => pos.position_title).join(', ')) : 'None').toString()}
                                                    </div>
                                                    
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                        user.isActive 
                                                            ? 'bg-green-100 text-green-800' 
                                                            : 'bg-red-100 text-red-800'
                                                    }`}>
                                                        {user.isActive ? 'Active' : 'Inactive'}
                                                    </span>
                                                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {user.tenantData?.customerName}
                                                    </span>
                                                </div>
                                            </div>
                                            <Button variant="outline" size="sm" onClick={(e) => {
                                                e.stopPropagation();
                                                handleEditUser(user);
                                            }}>
                                                <PencilIcon className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ))
                                ) : (
                                    <div className="text-center py-8">
                                        <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
                                        <p className="mt-1 text-sm text-gray-500">
                                            {searchTerm ? 'No users match your search.' : 'Get started by adding a new user.'}
                                        </p>
                                    </div>
                                )}
                            </div>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="flex items-center justify-between border-t border-gray-200 pt-4">
                                    <div className="text-sm text-gray-700">
                                        Showing <span className="font-medium">{indexOfFirstUser + 1}</span> to{' '}
                                        <span className="font-medium">
                                            {Math.min(indexOfLastUser, filteredUsers.length)}
                                        </span>{' '}
                                        of <span className="font-medium">{filteredUsers.length}</span> users
                                    </div>
                                    <div className="flex space-x-2">
                                        <Button
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                            variant="outline"
                                            size="sm"
                                        >
                                            Previous
                                        </Button>
                                        {Array.from({ length: totalPages }, (_, i) => (
                                            <Button
                                                key={i + 1}
                                                onClick={() => handlePageChange(i + 1)}
                                                variant={currentPage === i + 1 ? "default" : "outline"}
                                                size="sm"
                                            >
                                                {i + 1}
                                            </Button>
                                        ))}
                                        <Button
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                            variant="outline"
                                            size="sm"
                                        >
                                            Next
                                        </Button>
                                    </div>
                                </div>
                            )}
                            <div>
                                invitations... ayrı tab mı?
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Edit Users Modal */}
            <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
                <DialogContent aria-description='User Information' className="max-w-4xl max-h-[90vh] overflow-y-auto" aria-describedby="edit-user-description">
                    <DialogHeader>
                        <DialogTitle>User Information 
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                selectedUser?.isActive 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-red-100 text-red-800'
                            }`}>
                                {selectedUser?.isActive ? 'Active' : 'Inactive'}
                            </span>
                        </DialogTitle>
                        <p id="edit-user-description" className="sr-only">
                            View and edit user information including positions and roles
                        </p>
                    </DialogHeader>
                    {selectedUser && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
                            {/* User Basic Information */}
                            <div className="space-y-4">
                                <div className="border-b pb-2">
                                    <h3 className="text-lg font-medium">Kullanici Bilgileri</h3>
                                </div>
                                
                                <div className="space-y-3">
                                    <div>
                                        <Label className="text-sm font-medium text-gray-500">Name</Label>
                                        <Input
                                            name="name"
                                            value={editUserData.name}
                                            onChange={handleEditUserChange}
                                            className="mt-1"
                                        />
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-500">Email</Label>
                                        <Input
                                            name="email"
                                            value={editUserData.email}
                                            onChange={handleEditUserChange}
                                            className="mt-1"
                                        />
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-500">Phone</Label>
                                        <Input
                                            name="phone"
                                            value={editUserData.phone}
                                            onChange={handleEditUserChange}
                                            className="mt-1"
                                        />
                                    </div>

                                    <div>
                                        <Label className="text-sm font-medium text-gray-500">Created At</Label>
                                        <p className="text-sm font-medium">
                                            {new Date(selectedUser.createdAt).toLocaleDateString()}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* User Profile Information */}
                                <div className="space-y-4">
                                    <div className="border-b pb-2">
                                        <h3 className="text-lg font-medium">User Positions</h3>
                                    </div>

                                    <div className="space-y-3">
                                        <div className="relative">
                                            <Input
                                                type="text"
                                                placeholder="Search positions..."
                                                value={positionSearchTerm}
                                                onChange={(e) => setPositionSearchTerm(e.target.value)}
                                                className="mb-2"
                                            />
                                            <div className="max-h-40 overflow-y-auto border rounded-md">
                                                {filteredPositions.map((position) => (
                                                    <div 
                                                        key={position._id} 
                                                        className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
                                                        onClick={() => handlePositionChange(position._id)}
                                                    >
                                                        <input
                                                            type="checkbox"
                                                            checked={selectedPositions.includes(position._id)}
                                                            onChange={() => handlePositionChange(position._id)}
                                                            className="mr-2"
                                                        />
                                                        <span className="text-sm">{position.position_title_txt}</span>
                                                    </div>
                                                ))}
                                                {filteredPositions.length === 0 && (
                                                    <div className="p-2 text-sm text-gray-500">
                                                        No positions found
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                        <div className="mt-2">
                                            <h4 className="text-sm font-medium mb-1">Selected Positions:</h4>
                                            <div className="flex flex-wrap gap-1">
                                                {selectedPositions.map((positionId) => {
                                                    const position = customerPositions?.find(p => p._id === positionId);
                                                    return position ? (
                                                        <span 
                                                            key={positionId} 
                                                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                                        >
                                                            {position.position_title_txt}
                                                            <button
                                                                type="button"
                                                                className="ml-1 inline-flex items-center justify-center rounded-full bg-blue-200 text-blue-800 hover:bg-blue-300 focus:outline-none"
                                                                onClick={() => handlePositionChange(positionId)}
                                                            >
                                                                <span className="sr-only">Remove</span>
                                                                <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                                </svg>
                                                            </button>
                                                        </span>
                                                    ) : null;
                                                })}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    )}
                    <div>
                        Yetkiler... Position vs Roles {'->'} Buna gore dept. bilgisi.
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={handleCloseModal}>
                            Close
                        </Button>
                        <Button type="submit" onClick={handleSaveEdit}>
                            Save Changes
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

export default AccountUsersInfoCard;