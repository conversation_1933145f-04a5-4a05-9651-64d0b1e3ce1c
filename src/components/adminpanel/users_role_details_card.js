import { useState, useEffect } from 'react';
import { useApiClient } from '@/lib/fnx/fnx.apiClient';
import { useToast } from '@/lib/hooks/use-toast';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';

import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';

import { Badge } from '@/components/ui/badge';

const RolesDetailsCard = (props) => {
    const {session, selectedRole} = props;
    const { post, get, put, delete: deleteRequest } = useApiClient();
    const { toast } = useToast();
    const [roleData, setRoleData] = useState(props.selectedRole || null);
    const [roleDataLoading, setRoleDataLoading] = useState(false);


    const getRolebyId = async () => {
        try {
            setRoleDataLoading(true);
            const res = await fetch(`/api/tenant/role_detail/${props.selectedRole._id}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${session?.token}`
                }
            });
            setRoleDataLoading(false);
            if (res.ok) {
                const data = await res.json();
                // console.log('role:', data);
                setRoleData(data.data || []);
            }
        } catch (error) {
            setRoleDataLoading(false);
            console.error('Error fetching customer info:', error);
        }
    };

    useEffect(() => { 
        // props.selectedRole && console.log('selectedRole', selectedRole);
        setRoleData(props.selectedRole || []);
        props.selectedRole && getRolebyId();
    }, [props.selectedRole]);

    const role = roleData || props.selectedRole;

    return (
        <div className="p-4 bg-white rounded-lg border border-gray-200">
            <div className="flex justify-between items-center pb-4">
                <h2 className="text-lg font-medium">Role Details {selectedRole ? ' / ' + selectedRole?.roleName : ''}</h2>
                <Button type="button" onClick={() => {
                    setRoleData(null);props.setSelectedRole && props.setSelectedRole(null)}} variant="outline" size="sm">
                    x
                </Button>
            </div>

                    <span>tanimli roller - async card..</span>
            {(props.selectedRole || roleData) && role && (
                <div className="space-y-6">
                    {/* Role Overview */}
                    <Card>
                        <CardHeader>
                            <div className="flex justify-between items-start">
                                <div>
                                    <CardTitle className="text-xl" title={'created at ' + new Date(role.createdAt).toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    }) + ', last updated at ' + new Date(role.updatedAt).toLocaleDateString('en-US', {
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                    })}>{role.roleName}</CardTitle>
                                    {/* <CardDescription>Role Information</CardDescription> */}
                                </div>
                                <Badge 
                                    title={role._id}
                                    className={role.status === 'active' ? 'bg-green-100 text-green-800 hover:bg-green-100' : 'bg-red-100 text-red-800 hover:bg-red-100'}
                                >
                                    {role.status}
                                </Badge>
                            </div>
                        </CardHeader>
                        <CardContent>
                            {role.description && (
                                <div className="py-2">
                                    <p className="text-sm text-gray-700">{role.description}</p>
                                </div>
                            )}

                            {role.responsibilities && (
                                <div className="py-2">
                                    <h4 className="text-xs font-medium text-gray-500 italic">Responsibilities</h4>
                                    <p className="text-sm text-gray-700">{role.responsibilities}</p>
                                </div>
                            )}
                            
                            {role.permissions && role.permissions.length > 0 && (
                                <div className="py-2">
                                    <h4 className="text-xs font-medium text-gray-500 italic pb-2">Permissions</h4>
                                    <div className="flex flex-wrap gap-2">
                                        {role.permissions.map((permission, index) => (
                                            <Badge key={index} variant="secondary">
                                                {permission}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}
                            
                        </CardContent>
                    </Card>

                    {/* Detailed Permissions */}
                    {role.permissions_detailed && role.permissions_detailed.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Permission Details</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {role.permissions_detailed.map((permission, index) => (
                                        <div key={index} className="border rounded-lg p-4">
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <h4 className="font-medium">{permission.moduleTitle}</h4>
                                                    <p className="text-sm text-gray-600">{permission.desc}</p>
                                                </div>
                                                <Badge variant="outline">{permission.code}</Badge>
                                            </div>
                                            <div className="mt-2">
                                                <span className="text-xs text-gray-500">Module: {permission.module}</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            )}
        </div>
    )
}

export default RolesDetailsCard;
