import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';

const ProtectedRoute = ({ children, requireAdmin = false, requireOnboarding = false }) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  useEffect(() => {
    if (status === 'loading') return; // Still loading

    if (status === 'unauthenticated') {
      // User is not authenticated, redirect to signin
      router.push('/auth/signin');
      return;
    }

    if (session?.user) {
      // Check admin requirement
      if (requireAdmin && session.user.role !== 'admin') {
        router.push('/dashboard'); // Redirect non-admin users
        return;
      }

      // Check onboarding requirement
      if (requireOnboarding && !session.user.onboardingCompleted) {
        router.push('/onboarding');
        return;
      }

      // If user is not onboarded and we're not on onboarding page
      if (!requireOnboarding && !session.user.onboardingCompleted && router.pathname !== '/onboarding') {
        router.push('/onboarding');
        return;
      }
    }
  }, [session, status, router, requireAdmin, requireOnboarding]);

  // Show loading spinner while checking authentication
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render children if user is not authenticated
  if (status === 'unauthenticated') {
    return null;
  }

  // Don't render children if admin is required but user is not admin
  if (requireAdmin && session?.user?.role !== 'admin') {
    return null;
  }

  // Don't render children if onboarding is required but not completed
  if (requireOnboarding && !session?.user?.onboardingCompleted) {
    return null;
  }

  return children;
};

export default ProtectedRoute;
