import { useRouter } from 'next/router';
import Link from 'next/link';

export function LanguageSwitcher() {
  const router = useRouter();
  const { pathname, asPath, query, locale } = router;

  return (
    <div className="flex items-center gap-2">
      <Link
        href={{ pathname, query }}
        as={asPath}
        locale="en"
        className={`px-2 py-1 rounded ${
          locale === 'en' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'
        }`}
      >
        EN
      </Link>
      <Link
        href={{ pathname, query }}
        as={asPath}
        locale="tr"
        className={`px-2 py-1 rounded ${
          locale === 'tr' ? 'bg-blue-500 text-white' : 'bg-gray-200 dark:bg-gray-700'
        }`}
      >
        TR
      </Link>
    </div>
  );
}