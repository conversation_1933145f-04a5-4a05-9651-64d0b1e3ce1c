import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';

const SearchableSelect = React.forwardRef(({ 
    options = [], 
    value, 
    onValueChange, 
    placeholder = "Select an option...",
    emptyMessage = "No options found",
    className,
    multiple = false,
    ...props 
}, ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const containerRef = useRef(null);
    const inputRef = useRef(null);

    // Filter options based on search term
    const filteredOptions = options.filter(option => 
        option.label && option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                setIsOpen(false);
                setSearchTerm('');
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    // Focus input when dropdown opens
    useEffect(() => {
        if (isOpen && inputRef.current) {
            inputRef.current.focus();
        }
    }, [isOpen]);

    const handleSelect = (optionValue) => {
        if (multiple) {
            // For multi-select, toggle the selected value in the array
            const currentValue = Array.isArray(value) ? value : [];
            if (currentValue.includes(optionValue)) {
                onValueChange(currentValue.filter(v => v !== optionValue));
            } else {
                onValueChange([...currentValue, optionValue]);
            }
        } else {
            // For single select, just set the value
            onValueChange(optionValue);
            setIsOpen(false);
            setSearchTerm('');
        }
    };

    const getSelectedLabel = () => {
        if (multiple) {
            // For multi-select, show count of selected items
            const selectedCount = Array.isArray(value) ? value.length : 0;
            if (selectedCount === 0) return placeholder;
            if (selectedCount === 1) {
                const selectedOption = options.find(option => option.value === value[0]);
                return selectedOption && selectedOption.label ? selectedOption.label : placeholder;
            }
            return `${selectedCount} items selected`;
        } else {
            // For single select, show the selected label
            const selectedOption = options.find(option => option.value === value);
            return selectedOption && selectedOption.label ? selectedOption.label : placeholder;
        }
    };

    const isSelected = (optionValue) => {
        if (multiple) {
            return Array.isArray(value) && value.includes(optionValue);
        }
        return value === optionValue;
    };

    return (
        <div ref={containerRef} className={cn("relative", className)} {...props}>
            <Button
                type="button"
                variant="outline"
                role="combobox"
                aria-expanded={isOpen}
                className="w-full justify-between"
                onClick={() => setIsOpen(!isOpen)}
            >
                <span className="truncate">{getSelectedLabel()}</span>
                <span className="ml-2">▼</span>
            </Button>

            {isOpen && (
                <div className="absolute top-full left-0 w-full mt-1 z-50 bg-white border border-gray-200 rounded-md shadow-lg">
                    <div className="p-2">
                        <Input
                            ref={inputRef}
                            placeholder="Search..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full"
                        />
                    </div>
                    <ScrollArea className="h-60">
                        <div className="py-1">
                            {filteredOptions.length > 0 ? (
                                filteredOptions.map((option) => (
                                    <Button
                                        key={option.value}
                                        variant={isSelected(option.value) ? "default" : "ghost"}
                                        className="w-full justify-start rounded-none"
                                        onClick={() => handleSelect(option.value)}
                                    >
                                        {multiple && isSelected(option.value) && (
                                            <span className="mr-2">✓</span>
                                        )}
                                        {option.label}
                                    </Button>
                                ))
                            ) : (
                                <div className="py-2 text-center text-gray-500">
                                    {emptyMessage}
                                </div>
                            )}
                        </div>
                    </ScrollArea>
                </div>
            )}
        </div>
    );
});

SearchableSelect.displayName = "SearchableSelect";

export { SearchableSelect };
