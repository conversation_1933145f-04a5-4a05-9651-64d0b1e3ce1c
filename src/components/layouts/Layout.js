import React from 'react';
import Sidebar from '@/components/Sidebar';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { Bell, Mail, Menu } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  HomeIcon,
  ChartBarIcon,
  UsersIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  ShoppingBagIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

const topRightIconsDefault = () => (
  <div className="ml-4 flex items-center md:ml-6">
    <button className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
      <BellIcon className="h-6 w-6" />
    </button>
  </div>
)

const AdminLayout = ({
  isDrawerOpen,
  toggleDrawer,
  title,
  breadCrumbs = ['Home'],
  topRightIcons,
  noheader=true,
  children,
}) => {
  
  return (
  <div className="mx-auto bg-gray-50 max-w-[2048px]">
    <div className="flex min-w-[600px]">
      {/* Sidebar Component - Sticky positioned */}
      <div className="sticky top-0 h-screen overflow-y-auto flex-shrink-0">
        <Sidebar isDrawerOpen={isDrawerOpen} toggleDrawer={toggleDrawer} />
      </div>

      {/* Main Content Area */}
      <div className="flex flex-col flex-1 min-h-screen">
        {/* Sticky Header */}
        {!noheader && (
          <header className="flex items-center justify-between h-16 px-6 bg-white border-b border-gray-200 sticky top-0 z-10">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" onClick={toggleDrawer} className="mr-4 md:hidden">
              <Menu className="h-6 w-6" />
            </Button>
            <div className="flex flex-col">
              <h2 className="text-2xl font-bold text-gray-800 whitespace-nowrap truncate">{title || 'Dashboard Overview'}</h2>
              <nav className="text-sm text-gray-500">
                <ol className="flex list-none p-0 m-0">
                  <li className="flex items-center">
                    <Link href="/" className="hover:underline">{breadCrumbs[0] || 'Home'}</Link>
                    <span className="mx-2">/</span>
                  </li>
                  <li className="text-gray-700">{breadCrumbs[1] || 'Dashboard'}</li>
                </ol>
              </nav>
            </div>
          </div>
          {topRightIcons || topRightIconsDefault()} 
        </header>
        )}
          <main className={cn(
            // isCollapsed ? "lg:pl-[60px]" : "lg:pl-64",
            "ease transform-gpu transition-all duration-100 will-change-transform md:bg-gray-50 md:py-3 md:pr-3",
          )}>
            <div className="bg-white p-4 sm:p-6 md:rounded-lg md:border md:border-gray-200">
              {children}
            </div>
          </main>
        </div>
      </div>
  </div>
)};

export default AdminLayout;