import { useState } from 'react';
// import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useSession, signOut } from 'next-auth/react';
import ProtectedRoute from '../components/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Badge } from '../components/ui/badge';
import {
  UserIcon,
  CogIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  ArrowLeftIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

const steps = [
  {
    id: 'welcome',
    title: 'Welcome to Sales Dashboard AI',
    description: 'Let\'s get you set up for success',
    icon: SparklesIcon,
  },
  {
    id: 'profile',
    title: 'Complete Your Profile',
    description: 'Tell us about yourself and your role',
    icon: UserIcon,
  },
  {
    id: 'preferences',
    title: 'Set Your Preferences',
    description: 'Customize your dashboard experience',
    icon: CogIcon,
  },
  {
    id: 'goals',
    title: 'Define Your Goals',
    description: 'What do you want to achieve?',
    icon: ChartBarIcon,
  },
  {
    id: 'complete',
    title: 'You\'re All Set!',
    description: 'Welcome to your sales dashboard',
    icon: CheckCircleIcon,
  },
];

export default function Onboarding() {
  const { data: session } = useSession();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    profile: {
      phone: '',
      department: '',
      position: '',
      bio: '',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    preferences: {
      theme: 'system',
      notifications: {
        email: true,
        push: true,
        sales: true,
        reports: true,
      },
      dashboard: {
        layout: 'default',
        widgets: ['revenue', 'deals', 'customers', 'insights'],
      },
    },
    goals: {
      monthlyRevenue: '',
      monthlyDeals: '',
      focusAreas: [],
    },
  });

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/auth/signin' });
  };

  const handleInputChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  const handleNestedInputChange = (section, subsection, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [subsection]: {
          ...prev[section][subsection],
          [field]: value
        }
      }
    }));
  };

  const handleArrayToggle = (section, field, value) => {
    setFormData(prev => {
      const currentArray = prev[section][field] || [];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];
      
      return {
        ...prev,
        [section]: {
          ...prev[section],
          [field]: newArray
        }
      };
    });
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const completeOnboarding = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/onboarding', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
        credentials: 'include'
      });

      if (response.ok) {
        router.push('/dashboard');
      } else {
        console.error('Failed to complete onboarding');
      }
    } catch (error) {
      console.error('Error completing onboarding:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    const step = steps[currentStep];

    switch (step.id) {
      case 'welcome':
        return (
          <div className="text-center space-y-6">
            <div className="mx-auto w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center">
              <SparklesIcon className="h-12 w-12 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome, {session?.user?.name}!
              </h2>
              <p className="text-gray-600 max-w-md mx-auto">
                We're excited to help you supercharge your sales process with AI-powered insights and automation.
              </p>
            </div>
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">What you'll get:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• AI-powered sales insights and recommendations</li>
                <li>• Comprehensive pipeline management</li>
                <li>• Customer relationship tracking</li>
                <li>• Performance analytics and forecasting</li>
              </ul>
            </div>
          </div>
        );

      case 'profile':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={formData.profile.phone}
                  onChange={(e) => handleInputChange('profile', 'phone', e.target.value)}
                  placeholder="Enter your phone number"
                />
              </div>
              <div>
                <Label htmlFor="department">Department</Label>
                <Input
                  id="department"
                  value={formData.profile.department}
                  onChange={(e) => handleInputChange('profile', 'department', e.target.value)}
                  placeholder="e.g., Sales, Marketing"
                />
              </div>
              <div>
                <Label htmlFor="position">Position</Label>
                <Input
                  id="position"
                  value={formData.profile.position}
                  onChange={(e) => handleInputChange('profile', 'position', e.target.value)}
                  placeholder="e.g., Sales Manager, Account Executive"
                />
              </div>
              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <select
                  id="timezone"
                  value={formData.profile.timezone}
                  onChange={(e) => handleInputChange('profile', 'timezone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                  <option value="UTC">UTC</option>
                </select>
              </div>
            </div>
            <div>
              <Label htmlFor="bio">Bio (Optional)</Label>
              <textarea
                id="bio"
                value={formData.profile.bio}
                onChange={(e) => handleInputChange('profile', 'bio', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Tell us a bit about yourself and your role..."
              />
            </div>
          </div>
        );

      case 'preferences':
        return (
          <div className="space-y-6">
            <div>
              <Label className="text-base font-semibold">Theme Preference</Label>
              <div className="mt-2 space-y-2">
                {['light', 'dark', 'system'].map((theme) => (
                  <label key={theme} className="flex items-center">
                    <input
                      type="radio"
                      name="theme"
                      value={theme}
                      checked={formData.preferences.theme === theme}
                      onChange={(e) => handleInputChange('preferences', 'theme', e.target.value)}
                      className="mr-2"
                    />
                    <span className="capitalize">{theme}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-base font-semibold">Notification Preferences</Label>
              <div className="mt-2 space-y-2">
                {Object.entries(formData.preferences.notifications).map(([key, value]) => (
                  <label key={key} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={value}
                      onChange={(e) => handleNestedInputChange('preferences', 'notifications', key, e.target.checked)}
                      className="mr-2"
                    />
                    <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-base font-semibold">Dashboard Widgets</Label>
              <p className="text-sm text-gray-600 mb-2">Choose which widgets to display on your dashboard</p>
              <div className="grid grid-cols-2 gap-2">
                {['revenue', 'deals', 'customers', 'insights', 'activities', 'forecast'].map((widget) => (
                  <label key={widget} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.preferences.dashboard.widgets.includes(widget)}
                      onChange={() => handleArrayToggle('preferences.dashboard', 'widgets', widget)}
                      className="mr-2"
                    />
                    <span className="capitalize">{widget}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      case 'goals':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="monthlyRevenue">Monthly Revenue Goal</Label>
                <Input
                  id="monthlyRevenue"
                  type="number"
                  value={formData.goals.monthlyRevenue}
                  onChange={(e) => handleInputChange('goals', 'monthlyRevenue', e.target.value)}
                  placeholder="e.g., 50000"
                />
              </div>
              <div>
                <Label htmlFor="monthlyDeals">Monthly Deals Goal</Label>
                <Input
                  id="monthlyDeals"
                  type="number"
                  value={formData.goals.monthlyDeals}
                  onChange={(e) => handleInputChange('goals', 'monthlyDeals', e.target.value)}
                  placeholder="e.g., 10"
                />
              </div>
            </div>

            <div>
              <Label className="text-base font-semibold">Focus Areas</Label>
              <p className="text-sm text-gray-600 mb-2">What areas do you want to focus on?</p>
              <div className="grid grid-cols-2 gap-2">
                {[
                  'Lead Generation',
                  'Customer Retention',
                  'Deal Closing',
                  'Pipeline Management',
                  'Team Collaboration',
                  'Performance Analytics'
                ].map((area) => (
                  <label key={area} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.goals.focusAreas.includes(area)}
                      onChange={() => handleArrayToggle('goals', 'focusAreas', area)}
                      className="mr-2"
                    />
                    <span>{area}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        );

      case 'complete':
        return (
          <div className="text-center space-y-6">
            <div className="mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircleIcon className="h-12 w-12 text-green-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                You're All Set!
              </h2>
              <p className="text-gray-600 max-w-md mx-auto">
                Your sales dashboard is ready. Start exploring AI-powered insights and take your sales to the next level.
              </p>
            </div>
            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="font-semibold text-green-900 mb-2">Next Steps:</h3>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• Add your first customer</li>
                <li>• Create your first sales opportunity</li>
                <li>• Explore AI insights and recommendations</li>
                <li>• Customize your dashboard widgets</li>
              </ul>
            </div>
          </div>
        );

      default:
        return null;
    }
  }; 
  return (
    //<ProtectedRoute requireOnboarding={true}>
      <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <button
          onClick={handleLogout}
          className="absolute top-4 right-4 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded shadow transition"
        >
          Logout
        </button>
        <div className="max-w-2xl w-full space-y-8">
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>

          {/* Step Indicator */}
          <div className="flex justify-center space-x-2">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`w-3 h-3 rounded-full ${
                  index <= currentStep ? 'bg-blue-600' : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          {/* Main Content */}
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2">
                {
                  (() => {
                    const Icon = steps[currentStep].icon;
                    return <Icon className="h-6 w-6 text-blue-600" />;
                  })()
                }
                {steps[currentStep].title}
              </CardTitle>
              <CardDescription>
                {steps[currentStep].description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {renderStepContent()}
            </CardContent>
          </Card>

          {/* Navigation */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Previous
            </Button>

            {currentStep === steps.length - 1 ? (
              <Button onClick={completeOnboarding} disabled={loading}>
                {loading ? 'Setting up...' : 'Complete Setup'}
              </Button>
            ) : (
              <Button onClick={nextStep}>
                Next
                <ArrowRightIcon className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </div>
    //</ProtectedRoute>
  );
}
