// Simple protected endpoint for testing token refresh functionality

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // This endpoint is protected by middleware, so if we reach here, the token is valid
  const token = req.headers.token;
  
  return res.status(200).json({
    message: 'Successfully accessed protected endpoint',
    timestamp: new Date().toISOString(),
    tokenPresent: !!token,
    method: req.method
  });
}
