// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../../auth/[...nextauth]';
import { jwtVerify } from 'jose';

export default async function handler(req, res) {
    console.log('API: Request received for:', req.url);
    console.log('API: All headers:', Object.keys(req.headers));
    console.log('API: Token header exists:', 'token' in req.headers);

    // Önce NextAuth session'ını kontrol et
    const session = await getServerSession(req, res, authOptions);
    let user = session?.user;

    console.log('API: NextAuth session:', session ? 'EXISTS' : 'NOT_FOUND');

    // NextAuth session yoksa, middleware'den gelen JWT token'ını kontrol et
    if (!session) {
        const token = req.headers.token;
        console.log('API: Checking JWT token from middleware:', token ? token.substring(0, 20) + '...' : 'No token');
        console.log('API: Token header value:', token);

        if (token) {
            try {
                const key = process.env.JWT_KEY;
                if (key) {
                    const jwtResult = await jwtVerify(token, new TextEncoder().encode(key));
                    const payload = jwtResult.payload;

                    console.log('JWT payload:', payload);

                    if (payload && payload.exp && (Date.now() - payload.exp * 1000) < 0) {
                        // JWT token'dan user bilgilerini oluştur
                        user = {
                            id: payload.id,
                            email: payload.email,
                            tenantData: {
                                clientId: payload.clientId,
                                clientSchema: payload.clientSchema,
                                customerId: payload.customerId
                            }
                        };
                        console.log('JWT user created:', user);
                    }
                }
            } catch (error) {
                console.error('JWT verification error:', error);
            }
        }
    }

    if (!user) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    // console.log('Tenant Info API called', user);
    const { method, query } = req;
    const { offset = 0, limit = 10 } = req;

    switch (method) {
        case 'GET':
            try {
                let customerData = {};
                let dealers = [];
                if (user?.tenantData?.clientId) {
                    const dtBop = Date.now();
                    customerData = await getCustomerData(user.tenantData.clientId);
                    console.log('getCustomerData Elapsed', Date.now() - dtBop);
                    dealers = await getDealerList({
                        customerData, 
                        modMobile: true, 
                        offset,
                        limit,
                    }); 
                }
                res.status(200).json({ data: dealers, 
                    //customerData, user 
                });
            } catch (error) {
                console.error('Tenant Info API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        default:
            res.setHeader('Allow', ['GET']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}


const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}


const getDealerList = async ({
    customerData, modMobile = true,
    fields = false,
    offset = 0,
    limit = 10
}) => {
    try {
        const client = await clientPromise;
        let clientDB = customerData?.clientDB || 'tflouu_L0';
        let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
        clientDB = clientDB || 'tflouu_L0';
        clientSchema = clientSchema || 'vmrwaswszr';
        const dbCollName = `${clientSchema}${vars.db.actCollections.dealers}`;
        const dbClient = client.db(clientDB);
        const q = [];
        q.push({ $sort: { createdAt: -1 } });
        if (modMobile) {
            q.push({
                $project: {
                    id: "$_id",
                    _id: 0,
                    code: 1,
                    tip: 1,
                    segment: 1,
                    tabelaAdi: 1,
                    ticariUnvan: 1,
                    durum: 1,
                    konum: {
                        $concat: [
                            { $ifNull: ["$konum.ilce", ""] },
                            "/",
                            { $ifNull: ["$konum.sehir", ""] }
                        ]
                    },
                }
            });
        } else {
            q.push(
                {
                    $project: {
                        ...fields,
                    }
                }
            )
        };
        if (fields && Object.keys(fields).length > 0) {
            q.push({
                $project: {
                    ...fields,
                }
            });
        }

        

        if (offset && parseInt(offset) > 0) {
            q.push({ $skip: parseInt(offset, 10) });
        }
        if (limit && limit > 0) {
            q.push({ $limit: parseInt(limit, 10) });
        }


        // console.log('getDealerList - clientDB, dbCollName:', clientDB, dbCollName, JSON.stringify(q, null, 2));
        const results = await dbClient.collection(dbCollName).aggregate(q).toArray();
        // console.log('results - aggregation result:', JSON.stringify(results, null, 2));
        if (!results) {
            return null;
        }
        return results;
    } catch (error) {
        console.error('getDealerList error:', error);
        return null;
    }
}
