import { verifyUser } from "@/lib/fnx/fnx.auth";
import clientPromise from "@/lib/db/mongodb";

export default async function handler(req, res) {
    if (req.method !== 'POST') {
        return res.status(405).json({ message: 'Method not allowed' });
    }

    try {
        const { token } = req.body;
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }

        const dbConn = await clientPromise;
        const result = await verifyUser({ dbConn, token });

        return res.status(200).json(result);
    } catch (error) {
        console.error('Verification error:', error);
        return res.status(400).json({ 
            success: false,
            message: error.message || 'Error during verification'
        });
    }
}
