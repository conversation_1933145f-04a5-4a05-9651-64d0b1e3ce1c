import { OAuth2Client } from 'google-auth-library';
import { findUserByEmail, verifyPassword, checkInvitation, 
  markInvitationUsed, createUser, getTenantData, findTenantData, findPositionsAndRoles } from "@/lib/fnx/fnx.auth.utils";
import { jwtx } from "@/lib/fnx/fnx.auth";
import { vars } from '@/lib/constants';

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { email, password, clientId, googleUser, idToken } = req.body;

  // Handle Google Signin
  if (idToken) {
    return handleGoogleSignin(req, res, googleUser, clientId);
  }

  // Handle Email/Password Signin
  return handleCredentialsSignin(req, res, email, password, clientId);
}

async function handleCredentialsSignin(req, res, email, password, clientId) {
  if (!email || !password) {
    console.log('handleCredentialsSignin: Missing email or password');
    return res.status(400).json({ message: 'Email and password are required' });
  }

  try {
    // Find user in database
    let dtUser = Date.now();
    const user = await findUserByEmail(email, clientId);
    console.log('dtUser Elapsed', Date.now() - dtUser);

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    if (!user.tenantData) {
      console.log('handleCredentialsSignin: User has no tenant data', user);
      return res.status(401).json({ message: 'User has no tenant data' });
    }

    if (!user.tenantData?.isActive) {
      console.log('Tenant is not active', user.tenantData);
      return res.status(401).json({ message: 'Tenant is not active' });
    }

    // Verify password
    const isValid = await verifyPassword(password, user.password);
    if (!isValid) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    user.tenantData_Debug = user.tenantData
    const tenantData = await getTenantData({user, clientId});
    // console.log('tenantData', tenantData);
    // console.log('user.tenantData', user.tenantData)
    user.tenantData = tenantData;
    // console.log('handleCredentialsSignin user', JSON.stringify(user, null, 2));
    delete user.tenantData_Debug;
    delete user.selectedClientId;
    delete user.tenants; // Remove tenants before sending user object
    delete user.password; // Remove password before sending user object
    return generateAndReturnTokens(res, user, clientId);
    // TODO: log auth event: login

  } catch (error) {
    console.error('Signin error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

async function handleGoogleSignin(req, res, googleUser, clientId) {
  const { idToken } = req.body;

  if (!idToken) {
    return res.status(400).json({ message: 'ID token is required' });
  }

  try {
    // 1. Google ID Token doğrulama
    const ticket = await client.verifyIdToken({
      idToken,
      audience: process.env.GOOGLE_CLIENT_ID,
    });

    const payload = ticket.getPayload();
    console.log('Google payload:', payload);
    const { sub: googleId, email, name, picture } = payload;

    // 2. Kullanıcıyı bul veya oluştur
    let existingUser = await findUserByEmail(email, clientId);
    
    // If user doesn't exist, create new user (similar to NextAuth)
    if (!existingUser) {
      const invitation = await checkInvitation(email);
      if (!invitation) {
        return res.status(400).json({ message: 'No invitation found for this email' });
      }

      // Create new user
      const newUser = await createUser({
        email: email,
        name: name,
        image: picture,
        role: invitation.role || 'member',
        tenants: invitation.tenants || [],
        provider: 'google',
      });
      
      await markInvitationUsed(email);
      existingUser = {
        _id: newUser._id,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role
      };
    }

    existingUser.tenantData_Debug = existingUser.tenantData
    const tenantData = await getTenantData({user: existingUser, clientId});
    existingUser.tenantData = tenantData;
    delete existingUser.tenantData_Debug;
    delete existingUser.selectedClientId;
    delete existingUser.tenants;
    delete existingUser.password; 

    // Generate and return tokens
    return generateAndReturnTokens(res, existingUser, clientId);
  } catch (error) {
    console.error('Google signin error:', error);
    if (error.message.includes('Token used too late')) {
      return res.status(401).json({ message: 'Invalid Google token' });
    }
    return res.status(500).json({ message: 'Authentication failed' });
  }
}

async function generateAndReturnTokens(res, user, clientId) {
  try {
    // Generate JWT tokens
    const jwtPayload = {
      id: user._id.toString(),
      email: user.email,
      fullName: user.name,
      role: user.role,
      clientId: clientId || null,
    };

    const token = await jwtx.sign({
      payload: jwtPayload,
      lifeTime: vars.token.tokenlifeTime
    });

    const refreshToken = await jwtx.sign({
      payload: jwtPayload,
      lifeTime: vars.token.refreshtokenLifeTime
    });

    // Return tokens and user info
    return res.status(200).json({
      success: true,
      token,
      refreshToken,
      user: {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        role: user.role,
        image: user.image || null,
        preferences: user.preferences || {},
        profile: user.profile || {},
        positionsAndRoles: user.tenantData?.positionsAndRoles || [],
        // accessibleModules: user.tenantData?.accessibleModules || [],
        clientId: user.tenantData?.clientId || null,
        tenantId: user.tenantData?.id || null,
        tenantName: user.tenantData?.name || null,
        subsPlanID: user.tenantData?.subsPlanID || null,
        subsContractID: user.tenantData?.subsContractID || null,
        isTenantAdmin: user.tenantData?.isTenantAdmin || false, 
        // tenantData: user.tenantData || {}
      }
    });
  } catch (error) {
    console.error('Token generation error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

// function getModulesWithAccessPermission(user) {
//   // Sonuçları tutmak için Set (tekrar etmemesi için)
//   const modules = new Set();

//   // tenantData.positionsAndRoles dizisini kontrol et
//   if (user.tenantData?.positionsAndRoles && Array.isArray(user.tenantData.positionsAndRoles)) {
//     user.tenantData.positionsAndRoles.forEach(pos => {
//       if (pos.roles && Array.isArray(pos.roles)) {
//         pos.roles.forEach(role => {
//           if (role.permissions && Array.isArray(role.permissions)) {
//             role.permissions.forEach(perm => {
//               if (perm.permission === "access") {
//                 modules.add(perm.module);
//               }
//             });
//           }
//         });
//       }
//     });
//   }

//   // Set'i diziye çevir ve döndür
//   return Array.from(modules);
// }

/*
find positions with roles...

db.getCollection('vmrwaswszr.app.auth.positions').aggregate([
  // 1. position_titles ile birleştir: position_title_id üzerinden
  {
    $lookup: {
      from: "vmrwaswszr.app.auth.position_titles",
      localField: "position_title_id",
      //foreignField: "position_title_id",
      foreignField: "_id",
      as: "position_title_info"
    }
  },
  {
    $unwind: {
      path: "$position_title_info",
      preserveNullAndEmptyArrays: true
    }
  },

  // 2. position_roles ile birleştir: position_id üzerinden
  {
    $lookup: {
      from: "vmrwaswszr.app.auth.position_roles",
      localField: "_id",
      foreignField: "position_id",
      as: "assigned_roles"
    }
  },

  // 3. assigned_roles içindeki role_ids ile roles koleksiyonuna eriş
  {
    $lookup: {
      from: "vmrwaswszr.app.auth.roles",
      localField: "assigned_roles.role_ids",
      foreignField: "_id",
      as: "role_details"
    }
  },

  // 4. Her role'un permissions alanını permissions koleksiyonu ile genişlet
  {
    $lookup: {
      from: "vmrwaswszr.app.auth.permissions",
      localField: "role_details.permissions",
      foreignField: "_id",
      as: "all_permissions"
    }
  },

  // 5. Sonuçları düzleştir ve temizle
  {
    $project: {
      _id: 1,
      position_title: "$position_title_info.position_title",
      //position_title_code: "$position_title_info.position_title_code",
      //position_description: "$description",
      level: 1,
      grade: 1,
      status: 1,
      parent_position_id: 1,
      roles: {
        $map: {
          input: "$role_details",
          as: "role",
          in: {
            role_name: "$$role.roleName",
            role_description: "$$role.description",
            //responsibilities: "$$role.responsibilities",
            permissions: {
              $map: {
                input: {
                  $filter: {
                    input: "$all_permissions",
                    cond: {
                      $in: ["$$this._id", "$$role.permissions"]
                    }
                  }
                },
                as: "perm",
                in: {
                  permission_code: "$$perm.code",
                  module: "$$perm.module",
                  permission: "$$perm.permission",
                  access: "$$perm.access"
                }
              }
            }
          }
        }
      }
    }
  },

  // Opsiyonel: Sadece aktif pozisyonlar
  {
    $match: {
      status: "active"
    }
  }
])
  
*/
