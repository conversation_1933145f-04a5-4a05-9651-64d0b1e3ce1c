import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { MongoDBAdapter } from "@next-auth/mongodb-adapter";

import clientPromise from "@/lib/db/mongodb";
import {
  verifyPassword, findUserByEmail, createUser,
  checkInvitation, getModulesWithAccessPermission,
  markInvitationUsed, findTenantData, findPositionsAndRoles, getTenantData
} from "@/lib/fnx/fnx.auth.utils";
import { jwtx, saveToken, createJWTPayload } from "@/lib/fnx/fnx.auth";
import { vars } from '@/lib/constants';
// Temporary storage for tokens during sign-in process
const tokenStore = new Map();
// Temporary storage for clientId during Google sign-in process
const clientIdStore = new Map();
export const authOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      allowDangerousEmailAccountLinking: true,
      authorization: {
        params: {
          // prompt: "consent",
          scope: "openid email profile",
          // We'll add the clientId to the state parameter
        }
      }
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }
        // console.log('Credentials provided:', credentials);

        // Find user in database
        const user = await findUserByEmail(credentials.email, credentials.clientId);
        // console.log('user:', credentials, user);
        if (!user) {
          return null;
        }
 // Verify password
        const isValid = await verifyPassword(credentials.password, user.password);
        if (!isValid) {
          return null;
        }
        // console.log('externalPositionIds info found:', accessibleModules);

        const resp = {
          id: user._id.toString(),
          email: user.email,
          name: user.name,
          role: user.role,
          // user,
          // image: user.image,
        };
        return resp;
      },
    }),
  ],
  callbacks: {
    async redirect({ url, baseUrl }) {
      // Extract clientId from the callback URL and store it in session storage
      try {
        const urlObj = new URL(url);
        const clientId = urlObj.searchParams.get('cid');
        if (clientId) {
          // Store clientId with a timestamp for cleanup
          clientIdStore.set('pending_google_signin', {
            clientId,
            timestamp: Date.now()
          });
        }
      } catch (error) {
        // console.error('Error parsing redirect URL:', error);
      }

      // Return the original URL
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },

    async signIn(props) {
      const { user, account, profile, credentials } = props;
      // console.log('signIn props:', props);
      let existingUser;
      let userResult;
      let userAct = user || {};
      let userId = userAct?._id?.toString(); 
      let clientId = credentials?.clientId || null;
      if (account.provider === "google") {
        const pendingData = clientIdStore.get('pending_google_signin');
        if (pendingData && (Date.now() - pendingData.timestamp < 300000)) { // 5 minutes timeout
          // console.log('pendingData', pendingData, Date.now() - pendingData.timestamp, (Date.now() - pendingData.timestamp < 300000));
          clientId = pendingData.clientId;
          // Clear the pending storage
          clientIdStore.delete('pending_google_signin');
        }
      }
      existingUser = await findUserByEmail(user.email, clientId);
      userAct = existingUser || userAct;
      userId = userAct?._id?.toString() || user?.id?.toString();

      // Store clientId for JWT callback after we have the correct userId
      if (account.provider === "google" && clientId && userId) {
        clientIdStore.set(userId, clientId);
      }
      // userId = userAct?._id?.toString(); // || user?.id?.toString();
      // console.log('signIn props - userId:', props, userId);
      // For Google provider, get clientId from temporary storage
      // let clientId = null;
      if (account.provider === "google") {
        if (!existingUser) {
          const invitation = await checkInvitation(user.email);
          if (!invitation) {
            return false; // Reject sign-in if no invitation
          }

          // Create new user
          userResult = await createUser({
            email: user.email,
            name: user.name,
            image: user.image,
            role: invitation.role || 'member',
            tenants: invitation.tenants || [],
            provider: 'google',
          });
          userId = userResult?.insertedId?.toString() || userId;
          await markInvitationUsed(user.email);
        }
      }

      delete userAct?.password;
      let tenantData;
      try {
        console.log('Getting tenant data for user:', {
          email: userAct.email,
          provider: account.provider,
          clientId,
          hasTenantData: !!userAct.tenantData,
          tenants: userAct.tenants
        });
        tenantData = await getTenantData({ user: userAct, clientId });
        userAct.tenantData = tenantData;
        console.log('Tenant data retrieved:', { success: !!tenantData, tenantData });
      } catch (e) {
        console.log('Error getting tenantData:', e);
      }

      delete userAct.tenants; // Remove tenants before sending user object
      user.name = user.name || userAct.name; // Ensure name is set for new Google users
      let token;
      let refreshToken;
      try {
        const userForPayload = {
          _id: userId,
          email: existingUser?.email || user.email,
          name: existingUser?.name || user.name,
          role: existingUser?.role || 'member',
          clientId: clientId
        };

        const jwtPayload = createJWTPayload(userForPayload);

        token = await jwtx.sign({
          payload: jwtPayload,
          lifeTime: vars.token.tokenlifeTime
        });
        refreshToken = await jwtx.sign({
          payload: jwtPayload,
          lifeTime: vars.token.refreshtokenLifeTime
        });

      } catch (error) {
        console.error('Error generating tokens:', error);
        return false; // Token generation failed, reject sign-in
      }

      // Store tokens in temporary storage with user ID as key
      const userKey = userId || user.id;
      tokenStore.set(userKey, { token, refreshToken });
      
      // Save refresh token to database
      try {
        const client = await clientPromise;
        await saveToken({
          dbConn: client,
          payload: {
            email: existingUser?.email || user.email,
          },
          token: token,
          refreshToken: refreshToken,
          saveLogin: true
        });
      } catch (error) {
        console.error('Error saving refresh token to database:', error);
      };

      
      // Store tenantData in temporary storage for Google provider
      // For Credentials provider, we'll pass it directly through the user object
      if (account.provider === "google") {
        tokenStore.set(`tenantData_${userKey}`, userAct.tenantData);
        // user.tenantData = userAct.tenantData;
      } else {
        // Pass userAct data to the JWT callback for Credentials provider
        user.tenantData = userAct.tenantData;
      }

      // console.log('user', account.provider, JSON.stringify(user, null, 2));
      return true;
    },

    async jwt({ token, user, account, trigger, session }) {
      let dtBop = Date.now();

      // Handle session updates (when update() is called from client)
      if (trigger === 'update' && session?.token) {
        console.log('JWT callback: Updating token from session update');
        token.token = session.token;
        return token;
      }

      if (user) {
        const dbUser = await findUserByEmail(user.email);
        token.name = user.name || dbUser?.name;
        token.role = dbUser?.role || 'member';
        token.id = dbUser?._id?.toString() || user.id;
        token.onboardingCompleted = dbUser?.onboardingCompleted || false; // <-- EKLENDİ
        if (!dbUser) {
          return null;
        }
        // Retrieve tokens from temporary storage
        const userKey = token.id;
        const storedTokens = tokenStore.get(userKey);
        if (storedTokens) {
          token.token = storedTokens.token;
          token.refreshToken = storedTokens.refreshToken;
          // Clean up temporary storage
          tokenStore.delete(userKey);
        }

        // Retrieve and include clientId for Google provider
        const storedClientId = clientIdStore.get(userKey);
        if (storedClientId) {
          token.clientId = storedClientId;
          // Clean up temporary storage
          clientIdStore.delete(userKey);
        }

        // Retrieve tenantData from temporary storage for Google provider
        // For Credentials provider, it will be available directly in user.tenantData
        const storedTenantData = tokenStore.get(`tenantData_${userKey}`);
        console.log('JWT callback - retrieving tenantData:', {
          userKey,
          hasStoredTenantData: !!storedTenantData,
          hasUserTenantData: !!user?.tenantData,
          storedTenantData,
          userTenantData: user?.tenantData
        });

        if (storedTenantData) {
          token.userAct = {
            tenantData: storedTenantData
          };
          // Clean up temporary storage
          tokenStore.delete(`tenantData_${userKey}`);
        } else if (user?.tenantData) {
          // This handles the Credentials provider case
          token.userAct = {
            tenantData: user.tenantData
          };
        }
      }
      // console.log('jwt callback!:', { token, user, account }, Date.now() - dtBop);
      return token;
    },
    async session({ session, token }) {
      // console.log('session callback:', { session, token });

      session.user.role = token.role;
      session.user.id = token.id;
      session.user.onboardingCompleted = token.onboardingCompleted; // <-- EKLENDİ

      // Add token and refreshToken to session for both credentials and google providers
      if (token.token && token.refreshToken) {
        session.token = token.token;
        session.refreshToken = token.refreshToken;
      }

      // Add clientId to session if available
      if (token.clientId) {
        session.clientId = token.clientId;
        session.user.clientId = token.clientId; // Also add to user object for easier access
      }
      // Add userAct data to session if available
      if (token.userAct) {
        session.user.tenantData = token.userAct?.tenantData || {};
        // console.log('Session callback - tenantData set:', {
        //   hasTenantData: !!token.userAct?.tenantData,
        //   tenantData: token.userAct?.tenantData
        // });
      } else {
        // console.log('Session callback - no userAct in token:', { token });
      }


      return session;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
};

export default NextAuth(authOptions);
