import { getCollection } from '@/lib/db/mongodb';
import { sendPasswordResetEmail } from '@/lib/fnx/fnx.email';
import crypto from 'crypto';
import { vars } from "@/lib/constants"
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { email } = req.body;

    // console.log('Forgot password request for email:', email);
    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({ message: 'Invalid email format' });
    }

    // console.log('Looking for user with email:', email);


    const users = await getCollection(vars.db.collection.users);
    const user = await users.findOne({ email: email.toLowerCase() });

    // console.log('User found:', vars.db.collection.users, user);
    // Don't reveal if user exists or not for security reasons
    if (!user) {
      return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link' });
    }

    // Generate a reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date();
    resetTokenExpiry.setHours(resetTokenExpiry.getHours() + 1); // Token valid for 1 hour

    // Store the reset token and expiry in the user document
    await users.updateOne(
      { email: email.toLowerCase() },
      {
        $set: {
          resetToken,
          resetTokenExpiry,
        },
      }
    );

    // Send the password reset email
    await sendPasswordResetEmail({
      email: user.email,
      resetToken,
    });

    return res.status(200).json({
      message: 'If your email is registered, you will receive a password reset link',
    });
  } catch (error) {
    console.error('Password reset request error:', error);
    return res.status(500).json({ message: 'Error processing your request' });
  }
}
