import { getCollection } from '@/lib/db/mongodb';
import bcrypt from 'bcryptjs';
import { vars } from "@/lib/constants"

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { email } = req.body;
    const fixPassword = "Tatil2026!";

    const users = await getCollection(vars.db.collection.users);
    
    // Find user with the reset token and check if it's still valid
    const user = await users.findOne({
      email: email.toLowerCase(),});

    if (!user) {
      return res.status(400).json({ message: 'Invalid or expired reset token' });
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(fixPassword, 10);

    // Update the user's password and remove the reset token
    await users.updateOne(
      { _id: user._id },
      {
        $set: { password: hashedPassword },
        $unset: { resetToken: "", resetTokenExpiry: "" },
      }
    );

    return res.status(200).json({ message: 'Password has been reset successfully' });
  } catch (error) {
    console.error('Password reset error:', error);
    return res.status(500).json({ message: 'Error resetting password' });
  }
}
