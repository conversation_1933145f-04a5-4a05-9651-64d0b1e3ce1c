import { hashPassword, findUserByEmail, createUser, checkInvitation, markInvitationUsed } from '@/lib/firebase/utils';
import clientPromise from '@/lib/db/mongodb';

const invitationsCollection = 'tourai.main.dim.users.invitations';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const { name, email, password, invitationCode } = req.body;

  // Validation
  if (!name || !email || !password || !invitationCode) {
    return res.status(400).json({ message: 'All fields are required' });
  }

  if (password.length < 8) {
    return res.status(400).json({ message: 'Password must be at least 8 characters long' });
  }

  try {
    // Check if user already exists
    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Verify invitation code
    const client = await clientPromise;
    const db = client.db('toursdb');;
    
    const invitation = await db.collection(invitationsCollection).findOne({
      code: invitationCode,
      status: 'pending',
      expiresAt: { $gt: new Date() }
    });

    if (!invitation) {
      return res.status(400).json({ message: 'Invalid or expired invitation code' });
    }

    // Check if invitation email matches
    if (invitation.email && invitation.email !== email) {
      return res.status(400).json({ message: 'Email does not match invitation' });
    }

    // Hash password
    const hashedPassword = await hashPassword(password);

    // Create user
    const userData = {
      name,
      email,
      password: hashedPassword,
      role: invitation.role || 'member',
      provider: 'credentials',
    };

    const newUser = await createUser(userData);

    // Mark invitation as used
    await db.collection(invitationsCollection).updateOne(
      { code: invitationCode },
      { 
        $set: { 
          status: 'used',
          usedAt: new Date(),
          usedBy: newUser._id
        }
      }
    );

    // Remove password from response
    const { password: _, ...userResponse } = newUser;

    res.status(201).json({
      message: 'User created successfully',
      user: userResponse
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
