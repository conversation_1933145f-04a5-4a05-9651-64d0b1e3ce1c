import clientPromise, { getCollection } from "@/lib/db/mongodb";
import { vars } from '@/lib/constants';
import * as fnxAuth from "@/lib/fnx/fnx.auth";

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { refreshToken, deviceId, appVersion } = req.body;
    console.log('Mobile refresh token request received');
    
    if (!refreshToken) {
      return res.status(400).json({ 
        message: 'Refresh token is required',
        error: 'MISSING_REFRESH_TOKEN'
      });
    }

    // Verify refresh token
    const key = process.env.JWT_KEY;
    if (!key) {
      console.error('JWT_KEY environment variable is not set');
      return res.status(500).json({ 
        message: 'Server configuration error',
        error: 'MISSING_JWT_KEY'
      });
    }

    // Validate refresh token
    const tokenValidation = await fnxAuth.verifytoken(refreshToken, key);
    if (!tokenValidation.isValid) {
      console.log('Invalid or expired refresh token for mobile app');
      return res.status(401).json({ 
        message: 'Invalid or expired refresh token',
        error: 'INVALID_REFRESH_TOKEN',
        requiresLogin: true // Mobil uygulamaya login gerektiğini bildir
      });
    }

    const decoded = tokenValidation.payload;
    console.log('Mobile refresh token validated for user:', decoded.email);
    
    // Find user and verify refresh token is stored
    const dbConn = await clientPromise;
    const users = await getCollection(vars.db.collection.users);
    const user = await users.findOne({
      email: decoded.email,
      refreshToken: refreshToken // Ensure stored refresh token matches
    });

    if (!user) {
      console.log('User not found or refresh token mismatch for mobile user:', decoded.email);
      return res.status(401).json({ 
        message: 'Invalid refresh token',
        error: 'USER_NOT_FOUND_OR_TOKEN_MISMATCH',
        requiresLogin: true
      });
    }

    // Create standardized JWT payload
    const jwtPayload = fnxAuth.createJWTPayload(user);

    // Generate new access token
    const newToken = await fnxAuth.jwtx.sign({
      payload: jwtPayload, 
      lifeTime: vars.token.tokenlifeTime
    });

    // Generate new refresh token for mobile (optional - for enhanced security)
    const newRefreshToken = await fnxAuth.jwtx.sign({
      payload: jwtPayload,
      lifeTime: vars.token.refreshtokenLifeTime
    });
    
    // Save both tokens to the database
    await fnxAuth.saveToken({
      dbConn: dbConn,
      payload: { email: user.email },
      token: newToken,
      refreshToken: newRefreshToken, // Update with new refresh token
      saveLogin: false
    });

    // Log mobile app info if provided
    if (deviceId || appVersion) {
      console.log('Mobile app info:', { deviceId, appVersion, userEmail: user.email });
    }
    
    console.log('Mobile token refresh successful for user:', user.email);
    
    return res.status(200).json({
      token: newToken,
      refreshToken: newRefreshToken, // Mobil uygulamaya yeni refresh token ver
      expiresIn: vars.token.tokenlifeTime,
      tokenType: 'Bearer',
      message: 'Mobile token refresh successful'
    });

  } catch (error) {
    console.error('Mobile refresh token error:', error);
    
    // Provide more specific error messages for mobile
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        message: 'Invalid refresh token format',
        error: 'MALFORMED_TOKEN',
        requiresLogin: true
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        message: 'Refresh token has expired',
        error: 'EXPIRED_REFRESH_TOKEN',
        requiresLogin: true
      });
    }
    
    return res.status(500).json({ 
      message: 'Internal server error during mobile token refresh',
      error: 'INTERNAL_ERROR'
    });
  }
}
