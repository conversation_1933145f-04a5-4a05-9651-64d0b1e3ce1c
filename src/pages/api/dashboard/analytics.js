import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { SaleModel } from '@/lib/db/models/Sale';
import { CustomerModel } from '@/lib/db/models/Customer';
import { ProductModel } from '@/lib/db/models/Product';
import { UserModel } from '@/lib/db/models/User';
import { ObjectId } from 'mongodb';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { method } = req;

  if (method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).end(`Method ${method} Not Allowed`);
  }

  try {
    const { timeframe = '30d', userId } = req.query;
    
    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    
    switch (timeframe) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Build filter based on user role and permissions
    const salesFilter = {
      createdAt: { $gte: startDate, $lte: endDate }
    };
    
    const customersFilter = {
      createdAt: { $gte: startDate, $lte: endDate }
    };

    // Non-admin users can only see their own data
    if (session.user.role !== 'admin') {
      salesFilter.assignedTo = new ObjectId(session.user.id);
      customersFilter.assignedTo = new ObjectId(session.user.id);
    } else if (userId) {
      // Admin viewing specific user's data
      salesFilter.assignedTo = new ObjectId(userId);
      customersFilter.assignedTo = new ObjectId(userId);
    }

    // Get analytics data
    const [
      salesAnalytics,
      customerStats,
      productStats,
      userCount
    ] = await Promise.all([
      SaleModel.getAnalytics(salesFilter),
      CustomerModel.getStats(customersFilter),
      ProductModel.getStats(),
      session.user.role === 'admin' ? UserModel.findAll({}, { limit: 1 }) : null
    ]);

    // Calculate conversion rate
    const conversionRate = salesAnalytics.totalDeals > 0 
      ? (salesAnalytics.wonDeals / salesAnalytics.totalDeals) * 100 
      : 0;

    // Calculate win rate
    const closedDeals = salesAnalytics.wonDeals + salesAnalytics.lostDeals;
    const winRate = closedDeals > 0 
      ? (salesAnalytics.wonDeals / closedDeals) * 100 
      : 0;

    const analytics = {
      sales: {
        totalValue: salesAnalytics.totalValue || 0,
        totalDeals: salesAnalytics.totalDeals || 0,
        avgDealSize: salesAnalytics.avgDealSize || 0,
        wonDeals: salesAnalytics.wonDeals || 0,
        lostDeals: salesAnalytics.lostDeals || 0,
        wonValue: salesAnalytics.wonValue || 0,
        conversionRate: Math.round(conversionRate * 100) / 100,
        winRate: Math.round(winRate * 100) / 100,
      },
      customers: {
        totalCustomers: customerStats.totalCustomers || 0,
        activeCustomers: customerStats.activeCustomers || 0,
        prospects: customerStats.prospects || 0,
        customers: customerStats.customers || 0,
      },
      products: session.user.role === 'admin' ? {
        totalProducts: productStats.totalProducts || 0,
        activeProducts: productStats.activeProducts || 0,
        totalValue: productStats.totalValue || 0,
        avgPrice: productStats.avgPrice || 0,
        lowStockCount: productStats.lowStockCount || 0,
      } : null,
      users: session.user.role === 'admin' ? {
        totalUsers: userCount?.pagination?.total || 0,
      } : null,
      timeframe,
      generatedAt: new Date().toISOString(),
    };

    res.status(200).json(analytics);

  } catch (error) {
    console.error('Analytics API error:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}
