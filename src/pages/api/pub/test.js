
import { getCustomerDB } from '@/lib/db/pgManager.js';

export default async function handler(req, res) {
  const { customerId = '3015492a443ff70540c42329d9912819', query = 'select * FROM public.q_tesis_listesi limit 10;' } = req.query;

  try {
    const dtBop = Date.now();
    const pool = await getCustomerDB(customerId);
    const result = await pool.query(query); // Güvenli sorgu için parametrize edilmeli!
    res.json({ data: result.rows, execTime: Date.now() - dtBop });
  } catch (err) {
    console.log('Error in /api/pub/test:', err);
    res.status(500).json({ error: err.message });
  }
}