import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth]";
import { getCollection } from "@/lib/db/mongodb";
import { ObjectId } from "mongodb";

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ message: "Unauthorized" });
  }

  const users = await getCollection("users");
  const userId = session.user.id;

  if (req.method === "GET") {
    try {
      const user = await users.findOne({ _id: new ObjectId(userId) });

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Remove sensitive information
      const { password, ...userInfo } = user;

      return res.status(200).json(userInfo);
    } catch (error) {
      console.error("Error fetching user profile:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  } else if (req.method === "PUT") {
    try {
      const { name, bio, company, position } = req.body;

      // Validate required fields
      if (!name) {
        return res.status(400).json({ message: "Name is required" });
      }

      // Update user profile
      await users.updateOne(
        { _id: new ObjectId(userId) },
        {
          $set: {
            name,
            bio,
            company,
            position,
            updatedAt: new Date(),
          },
        }
      );

      return res.status(200).json({ message: "Profile updated successfully" });
    } catch (error) {
      console.error("Error updating user profile:", error);
      return res.status(500).json({ message: "Internal server error" });
    }
  } else {
    return res.status(405).json({ message: "Method not allowed" });
  }
}
