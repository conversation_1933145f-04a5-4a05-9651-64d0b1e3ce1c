// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant Info API called', user);
    const { method } = req;
    switch (method) {
        case 'POST':
            if (req.body.action === 'create') {
                try {
                    // Check if user is admin
                    if (!user?.tenantData?.isTenantAdmin) {
                        return res.status(403).json({ message: 'Forbidden: Only admins can create roles' });
                    }

                    const { roleName, description, responsibilities, permissions, status = 'active' } = req.body;

                    if (!roleName || !description || !responsibilities) {
                        return res.status(400).json({ message: 'Missing required fields' });
                    }

                    const client = await clientPromise;
                    let clientDB = user?.tenantData?.clientDB || 'tflouu_L0';
                    let clientSchema = user?.tenantData?.clientSchema || 'vmrwaswszr';
                    const rolesCollection = `${clientSchema}${vars.db.actCollections.roles}`;
                    const dbClient = client.db(clientDB);

                    const newRole = {
                        roleName,
                        description,
                        responsibilities,
                        permissions: permissions || [],
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        status
                    };

                    const result = await dbClient.collection(rolesCollection).insertOne(newRole);

                    // In newer MongoDB drivers, insertOne returns an object with insertedId
                    // We need to fetch the inserted document to return it
                    const insertedRole = await dbClient.collection(rolesCollection).findOne({ _id: result.insertedId });

                    res.status(201).json({ message: 'Role created successfully', data: insertedRole });
                } catch (error) {
                    console.error('Create Role API error:', error);
                    res.status(500).json({ message: 'Internal server error' });
                }
            } else {
                try {
                    let customerData = {};
                    let roles = [];
                    if (user?.tenantData?.clientId) {
                        customerData = await getCustomerData(user.tenantData.clientId);
                        roles = await getRolesData(user.tenantData.clientId, customerData);
                    }
                    res.status(200).json({ data: roles });
                } catch (error) {
                    console.error('Tenant Info API error:', error);
                    res.status(500).json({ message: 'Internal server error' });
                }
            }
            break;
        case 'PUT':
            try {
                // Check if user is admin
                if (!user?.tenantData?.isTenantAdmin) {
                    return res.status(403).json({ message: 'Forbidden: Only admins can update roles' });
                }
                
                if (req.body.action === 'update') {
                    // Handle role update
                    const { roleId, roleName, description, responsibilities, permissions } = req.body;
                    
                    if (!roleId || !roleName || !description || !responsibilities) {
                        return res.status(400).json({ message: 'Missing required fields' });
                    }
                    
                    const client = await clientPromise;
                    let clientDB = user?.tenantData?.clientDB || 'tflouu_L0';
                    let clientSchema = user?.tenantData?.clientSchema || 'vmrwaswszr';
                    const rolesCollection = `${clientSchema}${vars.db.actCollections.roles}`;
                    const dbClient = client.db(clientDB);
                    
                    // Update the role
                    const result = await dbClient.collection(rolesCollection).updateOne(
                        { _id: new ObjectId(roleId) },
                        {
                            $set: {
                                roleName,
                                description,
                                responsibilities,
                                permissions: permissions || [],
                                updatedAt: new Date()
                            }
                        }
                    );
                    
                    if (result.matchedCount === 0) {
                        return res.status(404).json({ message: 'Role not found' });
                    }
                    
                    // Fetch the updated role
                    const updatedRole = await dbClient.collection(rolesCollection).findOne({ _id: new ObjectId(roleId) });
                    
                    return res.status(200).json({ message: 'Role updated successfully', data: updatedRole });
                } else if (user?.tenantData?.clientId) {
                    const result = await updateRolesData(user.tenantData.clientId, user.tenantData);
                    res.status(200).json({ message: 'Roles updated successfully', data: result });
                } else {
                    res.status(400).json({ message: 'Client ID not found' });
                }
            } catch (error) {
                console.error('Update Roles API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
             
            
        case 'DELETE':
            try {
                // Check if user is admin
                if (!user?.tenantData?.isTenantAdmin) {
                    return res.status(403).json({ message: 'Forbidden: Only admins can delete roles' });
                }

                // Get roleId from request body
                const { roleId } = req.body;

                if (!roleId) {
                    return res.status(400).json({ message: 'Missing roleId' });
                }

                const client = await clientPromise;
                let clientDB = user?.tenantData?.clientDB || 'tflouu_L0';
                let clientSchema = user?.tenantData?.clientSchema || 'vmrwaswszr';
                const rolesCollection = `${clientSchema}${vars.db.actCollections.roles}`;
                const dbClient = client.db(clientDB);

                // Update the role status to "deleted" instead of actually deleting it
                const result = await dbClient.collection(rolesCollection).updateOne(
                    { _id: new ObjectId(roleId) },
                    {
                        $set: {
                            status: 'deleted',
                            updatedAt: new Date()
                        }
                    }
                );

                if (result.matchedCount === 0) {
                    return res.status(404).json({ message: 'Role not found' });
                }

                return res.status(200).json({ message: 'Role deleted successfully' });
            } catch (error) {
                console.error('Delete Role API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
        default:
            res.setHeader('Allow', ['POST', 'PUT', 'DELETE']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}


const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}


const getRolesData = async (clientId, customerData, fields = {
    createdAt: 0,
}) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.roles}`;
    const dbClient = client.db(clientDB);
    const q = [
        {
            $match: {
                "status": "active"
            }
        },
        {
            $project: {
                ...fields,
            }
        }
    ];
    const rolez = await dbClient.collection(dbTitleCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!rolez) {
        return null;
    }
    return rolez;
}

const updateRolesData = async (clientId, customerData) => {
    const client = await clientPromise;
    
    // Get the subanet database for modules
    const subanetDB = client.db(vars.db.dbName);
    const modulesCollection = vars.db.collection.modules;
    
    // Get client database and schema for roles
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const rolesCollection = `${clientSchema}${vars.db.actCollections.roles}`;
    
    // Get all modules from subanet database
    const modules = await subanetDB.collection(modulesCollection).find({ isActive: true }).toArray();
    
    // Get the client database
    const dbClient = client.db(clientDB);
    
    // First, remove all existing roles
    await dbClient.collection(rolesCollection).deleteMany({});
    
    // Process each module and add its roles
    let insertedCount = 0;
    for (const module of modules) {
        if (module.roles && Array.isArray(module.roles)) {
            // Create roles documents for this module
            const rolesDocs = module.roles.map(perm => ({
                module: module.moduleName,
                moduleTitle: module.moduleTitle,
                code: perm.code,
                desc: perm.desc,

                menuWeight: module.menuWeight,
                menuIcon: module.menuIcon,

                createdAt: new Date(),
                updatedAt: new Date()
            }));
            
            // Insert roles for this module
            if (rolesDocs.length > 0) {
                await dbClient.collection(rolesCollection).insertMany(rolesDocs);
                insertedCount += rolesDocs.length;
            }
        }
    }
    
    return {
        message: 'Roles updated successfully',
        modulesProcessed: modules.length,
        rolesInserted: insertedCount
    };
};
