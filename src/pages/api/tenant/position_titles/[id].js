import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../../auth/[...nextauth]';

export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }

    const user = session?.user;
    const { method } = req;
    const { id } = req.query;

    if (!ObjectId.isValid(id)) {
        return res.status(400).json({ message: 'Invalid ID format' });
    }

    switch (method) {
        case 'PUT':
            try {
                const { position_title } = req.body;
                console.log('PUT request received:', { id, position_title });
                
                if (!position_title) {
                    return res.status(400).json({ message: 'Position title is required' });
                }

                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    // console.log('Customer data retrieved:', { 
                    //     clientId: user.tenantData.clientId,
                    //     clientDB: customerData?.clientDB,
                    //     clientSchema: customerData?.clientSchema 
                    // });
                }

                // First check if the position title exists
                const existingTitle = await getPositionTitleById(customerData, id);
                console.log('Existing title:', existingTitle);

                if (!existingTitle) {
                    console.log('Position title not found with id:', id);
                    return res.status(404).json({ message: 'Position title not found' });
                }

                // Only update the fields we want to change
                const updatedData = {
                    position_title,   // Update the title
                    dtUpdated: new Date(),
                    updatedBy: user?.id || 'system'
                };

                const result = await updatePositionTitleData(customerData, id, updatedData);
                console.log('Update result:', result);

                // If we get here, we know the document exists and was updated
                if (result) {
                    return res.status(200).json({ 
                        success: true,
                        message: 'Position title updated successfully', 
                        data: result 
                    });
                }
                
                // Something went wrong with the update
                console.error('Failed to update position title:', { id, updatedData });
                return res.status(500).json({ 
                    success: false,
                    message: 'Failed to update position title' 
                });
            } catch (error) {
                console.error('Tenant Position Title Update API error:', error);
                return res.status(500).json({ message: 'Internal server error' });
            }
            break;

        case 'DELETE':
            try {
                // First check if the position title exists
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    // console.log('Customer data retrieved for delete:', { 
                    //     clientId: user.tenantData.clientId,
                    //     clientDB: customerData?.clientDB,
                    //     clientSchema: customerData?.clientSchema 
                    // });
                }

                const existingTitle = await getPositionTitleById(customerData, id);
                console.log('Existing title for delete:', existingTitle);

                if (!existingTitle) {
                    console.log('Position title not found for deletion with id:', id);
                    return res.status(404).json({ 
                        success: false,
                        message: 'Position title not found' 
                    });
                }

                const result = await deletePositionTitleData(customerData, id);
                console.log('Delete result:', result);

                if (result) {
                    return res.status(200).json({ 
                        success: true,
                        message: 'Position title deleted successfully',
                        data: result
                    });
                } else {
                    console.error('Failed to delete position title:', { id });
                    return res.status(500).json({ 
                        success: false,
                        message: 'Failed to delete position title' 
                    });
                }
            } catch (error) {
                console.error('Tenant Position Title Delete API error:', error);
                return res.status(500).json({ 
                    success: false,
                    message: 'Internal server error',
                    error: error.message 
                });
            }
            break;

        default:
            res.setHeader('Allow', ['PUT', 'DELETE']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}

const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}

const getPositionTitleById = async (customerData, id) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position_titles}`;
    const dbClient = client.db(clientDB);
    
    try {
        const title = await dbClient.collection(dbTitleCollName).findOne({
            _id: new ObjectId(id),
            $or: [
                { "status": { $exists: false } },
                { "status": null },
                { "status": "" },
                { "status": "active" }
            ]
        });
        console.log('Found title in DB:', title);
        return title;
    } catch (error) {
        console.error('Error getting position title:', error);
        return null;
    }
}

const updatePositionTitleData = async (customerData, id, updateData) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position_titles}`;
    const dbClient = client.db(clientDB);
    
    try {
        // Remove _id from updateData as MongoDB doesn't allow updating _id
        const { _id, ...dataToUpdate } = updateData;
        
        console.log('Updating position title with:', {
            id,
            dataToUpdate,
            collectionName: dbTitleCollName
        });

        const result = await dbClient.collection(dbTitleCollName).findOneAndUpdate(
            { _id: new ObjectId(id) },
            { $set: dataToUpdate },
            { 
                returnDocument: 'after'
            }
        );
        
        console.log('Update operation result:', result);
        
        // In MongoDB Node.js driver's findOneAndUpdate, the result is the document itself
        if (result) {
            return {
                ...result,
                _id: result._id.toString() // Convert ObjectId to string
            };
        }
        
        return null;
    } catch (error) {
        console.error('Error updating position title data:', error);
        return null;
    }
}

const deletePositionTitleData = async (customerData, id) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position_titles}`;
    const dbClient = client.db(clientDB);
    
    try {
        console.log('Attempting to delete position title:', {
            id,
            collectionName: dbTitleCollName
        });

        const result = await dbClient.collection(dbTitleCollName).findOneAndUpdate(
            { _id: new ObjectId(id) },
            { 
                $set: { 
                    status: 'deleted', 
                    dtUpdated: new Date() 
                } 
            },
            { returnDocument: 'after' }
        );
        
        console.log('Delete operation result:', result);
        
        // In MongoDB Node.js driver's findOneAndUpdate, the result is the document itself
        if (result) {
            return {
                ...result,
                _id: result._id.toString() // Convert ObjectId to string
            };
        }
        
        return null;
    } catch (error) {
        console.error('Error deleting position title data:', error);
        return null;
    }
}
