// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant User Info API called', user);
    const { method } = req;
    
    switch (method) {
        case 'POST':
            try {
                let positions = [];
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    positions = await getPositionsData(user.tenantData.clientId, customerData);
                }
                // console.log('customerUsersData:', customerUsersData.length, JSON.stringify(customerUsersData, null, 2), customerData);
                res.status(200).json({ data: positions });
            } catch (error) {
                console.error('Tenant positions Info API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
        case 'PUT':
            try {
                const { position_title } = req.body;
                
                if (!position_title) {
                    return res.status(400).json({ message: 'Position title is required' });
                }
                
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }
                
                // Create new position title object with required fields
                const newPositionTitle = {
                    position_title,
                    dtCreated: new Date(),
                    dtUpdated: new Date(),
                    createdBy: user?.id || 'system',
                    updatedBy: user?.id || 'system',
                    status: 'active'
                };
                
                // Save to database
                const result = await savePositionTitleData(customerData, newPositionTitle);
                
                if (result) {
                    res.status(200).json({ message: 'Position title created successfully', data: result });
                } else {
                    res.status(500).json({ message: 'Failed to create position title' });
                }
            } catch (error) {
                console.error('Tenant Position Title Create API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
        default:
            res.setHeader('Allow', ['POST', 'PUT']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}

const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}
const getPositionsData = async (clientId, customerData, fields = {
    //  _id: 0,
    createdAt: 0,
    // updatedAt: 1
}) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position_titles}`;
    const dbClient = client.db(clientDB);
    const q = [
        {
            $match: {
                $or: [
                    { "status": { $exists: false } },
                    { "status": null },
                    { "status": "" },
                    { "status": "active" }
                ]
            }
        },
        {
            $project: {
                ...fields,
            }
        }
    ];

    const positions = await dbClient.collection(dbTitleCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!positions) {
        return null;
    }
    return positions;
}

const savePositionTitleData = async (customerData, positionTitleData) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    clientDB = clientDB || 'tflouu_L0';
    clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position_titles}`;
    const dbClient = client.db(clientDB);
    
    try {
        const result = await dbClient.collection(dbTitleCollName).insertOne(positionTitleData);
        return result;
    } catch (error) {
        console.error('Error saving position title data:', error);
        return null;
    }
}
