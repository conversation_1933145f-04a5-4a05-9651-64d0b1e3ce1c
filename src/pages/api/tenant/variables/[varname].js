// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant Info API called', user);
    const { method, query } = req;
    const { varname } = req.query;

    switch (method) {
        case 'GET':
            try {
                let customerData = {};
                let varnData = [];
                if (user?.tenantData?.clientId) {
                    const dtBop = Date.now();
                    customerData = await getCustomerData(user.tenantData.clientId);
                    console.log('getCustomerData Elapsed', Date.now() - dtBop);
                    varnData = await getVariables({
                        customerData, 
                        variableName: varname,
                    }); 
                }
                res.status(200).json({ data: varnData, 
                    //customerData, user 
                });
            } catch (error) {
                console.error('Tenant Info API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        default:
            res.setHeader('Allow', ['GET']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}


const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}

const getVariables = async ({ customerData, variableName }) => {
        const client = await clientPromise;
        let clientDB = customerData?.clientDB || 'tflouu_L0';
        let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
        clientDB = clientDB || 'tflouu_L0';
        clientSchema = clientSchema || 'vmrwaswszr';
        const dbCollName = `${clientSchema}${vars.db.actCollections.variables}`;
        const dbClient = client.db(clientDB);
        const q = [];
        
        // Check if variableName contains comma and split if needed
        if (variableName && variableName.includes(',')) {
            const names = variableName.split(',').map(name => name.trim());
            q.push({
                $match: { name: { $in: names } }
            });
        } else {
            q.push({
                $match: { name: variableName }
            });
        }
        
        // console.log('getDealerList - clientDB, dbCollName:', clientDB, dbCollName, JSON.stringify(q, null, 2));
        const results = await dbClient.collection(dbCollName).aggregate(q).toArray();
        // console.log('results - aggregation result:', JSON.stringify(results, null, 2));
        if (!results) {
            return null;
        }
        
        // If only one result, return the first element directly
        if (results.length === 1) {
            return results[0];
        }
        
        return results;
}
