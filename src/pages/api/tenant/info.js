// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant Info API called', user);
    const { method } = req;
    switch (method) {
        case 'POST':
            try {
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }
                res.status(200).json({ data: {...customerData} });
            } catch (error) {
                console.error('Tenant Info API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        case 'PUT':
            try {
                const { customerName, info } = req.body;
                if (user?.tenantData?.clientId) {
                    const updatedData = await updateCustomerData(user.tenantData.clientId, { customerName, info });
                    res.status(200).json({ message: 'Customer data updated successfully', data: updatedData });
                } else {
                    res.status(400).json({ message: 'Client ID not found' });
                }
            } catch (error) {
                console.error('Tenant Update API error:', error);
                res.status(500).json({ message: 'Internal server error' });
            }
            break;
            
        default:
            res.setHeader('Allow', ['POST', 'PUT']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}


const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}

const updateCustomerData = async (clientId, updateData) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    
    // Prepare the update object
    const updateObj = {};
    
    // Update customerName if provided
    if (updateData.customerName !== undefined) {
        updateObj.customerName = updateData.customerName;
    }
    
    // Update info fields if provided
    if (updateData.info !== undefined) {
        for (const [key, value] of Object.entries(updateData.info)) {
            updateObj[`info.${key}`] = value;
        }
    }
    
    // Update the document
    const result = await db.collection(vars.db.collection.customers).updateOne(
        { clientId: clientId },
        { $set: updateObj }
    );
    
    if (result.matchedCount === 0) {
        throw new Error('Customer not found');
    }
    
    // Return updated data
    return await getCustomerData(clientId);
};
