import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../../auth/[...nextauth]';

export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }

    const user = session?.user;
    const { method } = req;
    const { id } = req.query;

    if (!ObjectId.isValid(id)) {
        return res.status(400).json({ message: 'Invalid ID format' });
    }

    switch (method) {
        case 'PUT':
            try {
                const updateData = req.body;
                // console.log('PUT request received:', { id, updateData });

                // Validate required fields
                if (!updateData) {
                    return res.status(400).json({ message: 'Update data is required' });
                }

                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    // console.log('Customer data retrieved:', {
                    //     clientId: user.tenantData.clientId,
                    //     clientDB: customerData?.clientDB,
                    //     clientSchema: customerData?.clientSchema
                    // });
                }

                // First check if the position exists
                const existingPosition = await getPositionById(customerData, id);
                // console.log('Existing position:', existingPosition);

                if (!existingPosition) {
                    console.log('Position not found with id:', id);
                    return res.status(404).json({ message: 'Position not found' });
                }

                // Only update the fields we want to change
                const updatedData = {
                    ...updateData,
                    updatedAt: new Date(),
                    updatedBy: user?.id || 'system'
                };

                // Remove fields that shouldn't be updated
                delete updatedData._id;
                delete updatedData.createdAt;

                const result = await updatePositionData(customerData, id, updatedData);
                // console.log('Update result:', result);

                // If we get here, we know the document exists and was updated
                if (result) {
                    return res.status(200).json({
                        success: true,
                        message: 'Position updated successfully',
                        data: result
                    });
                }

                // Something went wrong with the update
                console.error('Failed to update position:', { id, updatedData });
                return res.status(500).json({
                    success: false,
                    message: 'Failed to update position'
                });
            } catch (error) {
                console.error('Tenant Position Update API error:', error);
                return res.status(500).json({ message: 'Internal server error' });
            }
            break;

        case 'DELETE':
            try {
                // First check if the position exists
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                    // console.log('Customer data retrieved for delete:', {
                    //     clientId: user.tenantData.clientId,
                    //     clientDB: customerData?.clientDB,
                    //     clientSchema: customerData?.clientSchema
                    // });
                }

                const existingPosition = await getPositionById(customerData, id);
                console.log('Existing position for delete:', existingPosition);

                if (!existingPosition) {
                    console.log('Position not found for deletion with id:', id);
                    return res.status(404).json({
                        success: false,
                        message: 'Position not found'
                    });
                }

                const result = await deletePositionData(customerData, id);
                console.log('Delete result:', result);

                if (result) {
                    return res.status(200).json({
                        success: true,
                        message: 'Position deleted successfully',
                        data: result
                    });
                } else {
                    console.error('Failed to delete position:', { id });
                    return res.status(500).json({
                        success: false,
                        message: 'Failed to delete position'
                    });
                }
            } catch (error) {
                console.error('Tenant Position Delete API error:', error);
                return res.status(500).json({
                    success: false,
                    message: 'Internal server error',
                    error: error.message
                });
            }
            break;

        case 'GET':
            try {
                let customerData = {};
                if (user?.tenantData?.clientId) {
                    customerData = await getCustomerData(user.tenantData.clientId);
                }

                const position = await getPositionById(customerData, id);
                // console.log('Position:', position);

                if (!position) {
                    console.log('Position not found with id:', id);
                    return res.status(404).json({ message: 'Position not found' });
                }

                return res.status(200).json({
                    success: true,
                    message: 'Position found',
                    data: position
                });
            } catch (error) {
                console.error('Tenant Position Get API error:', error);
                return res.status(500).json({
                    success: false,
                    message: 'Internal server error',
                    error: error.message
                });
            }
            break;
        default:
            res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}

const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}

const getPositionById = async (customerData, id) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);

    try {
        const position = await dbClient.collection(dbTitleCollName).findOne({
            _id: new ObjectId(id),
            $or: [
                { "status": { $exists: false } },
                { "status": null },
                { "status": "" },
                { "status": "active" }
            ]
        });
        // console.log('Found position in DB:', position);
        return position;
    } catch (error) {
        console.error('Error getting position:', error);
        return null;
    }
}

const updatePositionData = async (customerData, id, updateData) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);

    try {
        // Remove _id from updateData as MongoDB doesn't allow updating _id
        const { _id, ...dataToUpdate } = updateData;

        if (dataToUpdate.position_title_id) {
            dataToUpdate.position_title_id = new ObjectId(dataToUpdate.position_title_id);
        }

        if (dataToUpdate.department_id) {
            dataToUpdate.department_id = new ObjectId(dataToUpdate.department_id);
        }

        if (dataToUpdate.parent_position_id) {
            dataToUpdate.parent_position_id = new ObjectId(dataToUpdate.parent_position_id);
        }

        if (dataToUpdate.updatedBy) {
            dataToUpdate.updatedBy = new ObjectId(dataToUpdate.updatedBy);
        }

        // console.log('Updating position with:', {
        //     id,
        //     dataToUpdate,
        //     collectionName: dbTitleCollName
        // });

        const result = await dbClient.collection(dbTitleCollName).findOneAndUpdate(
            { _id: new ObjectId(id) },
            { $set: dataToUpdate },
            {
                returnDocument: 'after'
            }
        );

        // console.log('Update operation result:', result);

        // In MongoDB Node.js driver's findOneAndUpdate, the result is the document itself
        if (result) {
            return {
                ...result,
                _id: result._id.toString() // Convert ObjectId to string
            };
        }

        return null;
    } catch (error) {
        console.error('Error updating position data:', error);
        return null;
    }
}

const deletePositionData = async (customerData, id) => {
    const client = await clientPromise;
    let clientDB = customerData?.clientDB || 'tflouu_L0';
    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    const dbTitleCollName = `${clientSchema}${vars.db.actCollections.position}`;
    const dbClient = client.db(clientDB);

    try {
        console.log('Attempting to delete position:', {
            id,
            collectionName: dbTitleCollName
        });

        const result = await dbClient.collection(dbTitleCollName).findOneAndUpdate(
            { _id: new ObjectId(id) },
            {
                $set: {
                    status: 'deleted',
                    updatedAt: new Date()
                }
            },
            { returnDocument: 'after' }
        );

        console.log('Delete operation result:', result);

        // In MongoDB Node.js driver's findOneAndUpdate, the result is the document itself
        if (result) {
            return {
                ...result,
                _id: result._id.toString() // Convert ObjectId to string
            };
        }

        return null;
    } catch (error) {
        console.error('Error deleting position data:', error);
        return null;
    }
}