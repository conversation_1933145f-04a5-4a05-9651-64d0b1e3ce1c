// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { getServerSession } from 'next-auth/next';
import { ObjectId } from 'mongodb';
import clientPromise from '@/lib/db/mongodb';
import { vars } from '@/lib/constants';
import { authOptions } from '../auth/[...nextauth]';
export default async function handler(req, res) {
    const session = await getServerSession(req, res, authOptions);

    if (!session) {
        return res.status(401).json({ message: 'Unauthorized' });
    }
    const user = session?.user;
    // console.log('Tenant User Info API called', user);
    const { method } = req;
    
    switch (method) {

        case 'POST':
            if (req.body.action === 'create') {
                try {
                    // Check if user is admin
                    if (!user?.tenantData?.isTenantAdmin) {
                        return res.status(403).json({ message: 'Forbidden: Only admins can create roles' });
                    }

                    let customerData = await getCustomerData(user.tenantData.clientId);
                    let clientDB = customerData?.clientDB || 'tflouu_L0';
                    let clientSchema = customerData?.clientSchema || 'vmrwaswszr';

                    const { name, email, provider = 'api', phone, positions, status = 'active' } = req.body;

                    if (!name || !email || !positions) {
                        return res.status(400).json({ message: 'Missing required fields' });
                    }

                    const client = await clientPromise;
                    const dbName = vars.db.dbName;
                    // console.log('user', JSON.stringify(user, null, 2));
                    // let clientDB = user?.tenantData?.clientDB || 'tflouu_L0';
                    // let clientSchema = user?.tenantData?.clientSchema || 'vmrwaswszr';
                    const rolesCollection = vars.db.collection.users; //`${clientSchema}${vars.db.actCollections.roles}`;
                    const dbClient = client.db(dbName);

                    const newUser = {
                        email,
                        name,
                        image: '',
                        provider,
                        role: 'member',
                        isActive: true,
                        status,
                        onboardingCompleted: true,
                        tenants: [
                            {
                                clientId: user.tenantData.clientId,
                                clientSchema: clientSchema,
                                clientDB: clientDB,
                                customerName: customerData?.customerName,
                                roleId: 3,
                                roleCode: 'user',
                                positions: positions
                            }
                        ],
                        preferences: {
                            theme: 'system',
                        },
                        profile: {
                            phone
                        },
                        createdAt: new Date(),
                        updatedAt: new Date()
                    };

                    const result = await dbClient.collection(rolesCollection).insertOne(newUser);
                    // In newer MongoDB drivers, insertOne returns an object with insertedId
                    // We need to fetch the inserted document to return it
                    const insertedRole = await dbClient.collection(rolesCollection).findOne({ _id: result.insertedId });

                    res.status(201).json({ message: 'User created successfully', data: insertedRole });
                } catch (error) {
                    console.error('Create Role API error:', error);
                    res.status(500).json({ message: 'Internal server error' });
                }
            } else {
                try {
                    let customerData = {};
                    let users = [];
                    if (user?.tenantData?.clientId) {
                        customerData = await getCustomerData(user.tenantData.clientId);
                        users = await getUsersData(user.tenantData.clientId, customerData);
                    }
                    res.status(200).json({ data: users });
                } catch (error) {
                    console.error('Tenant Info API error:', error);
                    res.status(500).json({ message: 'Internal server error' });
                }
            }
            break;

        case 'PUT':
            try {
                // Check if user is admin
                if (!user?.tenantData?.isTenantAdmin) {
                    return res.status(403).json({ message: 'Forbidden: Only admins can update users' });
                }
                
                const { userId, name, email, phone, positions } = req.body;
                
                // Handle both id and _id fields
                const userIdentifier = userId;
                
                if (user?.tenantData?.clientId) {
                    const updatedData = await updateUserPositions(user.tenantData.clientId, userIdentifier, { name, email, phone, positions });
                    res.status(200).json({ message: 'User data updated successfully', data: updatedData });
                } else {
                    res.status(400).json({ message: 'Client ID not found' });
                }
            } catch (error) {
                console.error('Tenant User Update API error:', error);
                res.status(500).json({ message: 'Internal server error', error: error.message });
            }
            break;
             
        default:
            res.setHeader('Allow', ['POST', 'PUT']);
            return res.status(405).end(`Method ${method} Not Allowed`);
    }
}

const getCustomerData = async (clientId) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    const tenant = await db.collection(vars.db.collection.customers).findOne({ clientId: clientId });
    if (!tenant) {
        return null;
    }
    delete tenant.dbConn;
    tenant.id = tenant._id.toString();
    delete tenant._id;
    return {
        ...tenant,
    };
}
 

const getUsersData = async (clientId, customerData, fields = {
    createdAt: 0,
}) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    // let clientDB = customerData?.clientDB || 'tflouu_L0';
    // let clientSchema = customerData?.clientSchema || 'vmrwaswszr';
    // clientDB = clientDB || 'tflouu_L0';
    // clientSchema = clientSchema || 'vmrwaswszr';
    const dbTitleCollName = vars.db.collection.users; //`${clientSchema}${vars.db.actCollections.roles}`;
    const dbClient = client.db(dbName);
    const q = [
        {
            $match: {
                "status": "active",
                "tenants.clientId": clientId,
                $or: [
                    { "status": { $exists: true, $in: ["active", null, ""] } },
                    { "status": { $exists: false } }
                ]
            }
        },
        { "$addFields": { "tenant": { "$first": "$tenants" } } },
        {
            $project: {
                ...fields,
            }
        }
    ];
    // console.log('getUsersData - aggregation query:', JSON.stringify(q, null, 2));
    const userz = await dbClient.collection(dbTitleCollName).aggregate(q).toArray();
    // console.log('getPositionsData - aggregation result:', JSON.stringify(tenant, null, 2));
    if (!userz) {
        return null;
    }
    return userz;
}

const updateUserPositions = async (clientId, userId, updateData) => {
    const client = await clientPromise;
    const dbName = vars.db.dbName;
    const db = client.db(dbName);
    
    // Convert userId to ObjectId if it's a string
    let userObjectId;
    try {
        userObjectId = new ObjectId(userId);
    } catch (error) {
        throw new Error('Invalid user ID format');
    }
    
    // Also try to find user by id field if _id search fails
    let userExists = await db.collection(vars.db.collection.users).findOne({ _id: userObjectId });
    if (!userExists) {
        userExists = await db.collection(vars.db.collection.users).findOne({ id: userId });
        if (userExists) {
            userObjectId = userExists._id;
        }
    }
    
    // If still not found, throw error
    if (!userExists) {
        throw new Error('User not found');
    }
    
    // Prepare the update object
    const updateObj = {
        updatedAt: new Date() // Always update the timestamp
    };
    
    // Update name if provided
    if (updateData.name !== undefined) {
        updateObj.name = updateData.name;
    }
    
    // Update email if provided
    if (updateData.email !== undefined) {
        updateObj.email = updateData.email;
    }
    
    // Update phone in profile if provided
    if (updateData.phone !== undefined) {
        updateObj['profile.phone'] = updateData.phone;
    }
    
    // Update positions in tenantData if provided
    if (updateData.positions !== undefined) {
        updateObj[`tenants.$[tenant].positions`] = updateData.positions;
    }
    
    // Update the document
    const result = await db.collection(vars.db.collection.users).updateOne(
        { _id: userObjectId },
        { $set: updateObj },
        {
            arrayFilters: [{ "tenant.clientId": clientId }]
        }
    );
    
    if (result.matchedCount === 0) {
        throw new Error('User not found or client ID mismatch');
    }
    
    // Return updated data
    let customerData = await getCustomerData(clientId);
    let customerUsersData = await getUsersData(clientId, customerData);
    // Find the updated user by either _id or id
    return customerUsersData.find(user =>
        user._id.toString() === userObjectId.toString() ||
        user.id === userId
    );
};