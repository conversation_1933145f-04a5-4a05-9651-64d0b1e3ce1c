import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
import { ArrowLeft } from 'lucide-react';
import * as Form from '@radix-ui/react-form';
import { useAuth } from '@/lib/contexts/AuthContext';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';
import { en } from '@/translations/en';
import { tr } from '@/translations/tr';

export default function ForgotPassword() {
  const router = useRouter();
  const { locale } = router;
  const t = locale === 'tr' ? tr : en;
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [backgroundImage, setBackgroundImage] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const { forgotPassword } = useAuth();

  useEffect(() => {
    // Check if user prefers dark mode
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setIsDarkMode(true);
    }

    // Set a random background image
    const images = [
      'https://source.unsplash.com/random/1920x1080/?abstract',
      'https://source.unsplash.com/random/1920x1080/?gradient',
      'https://source.unsplash.com/random/1920x1080/?pattern',
    ];
    setBackgroundImage(images[Math.floor(Math.random() * images.length)]);
  }, []);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError('');

      await forgotPassword(email);
      setSuccess(true);
    } catch (err) {
      setError(err.message || t.auth.forgotPassword.errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const goBack = (event) => {
    event.preventDefault();
    router.back();
  };

  return (
    <>
      <Head>
        <title>{t.auth.forgotPassword.title} | TFLOUU</title>
      </Head>

      <div
        className="relative flex items-center justify-center min-h-screen bg-cover bg-center bg-gray-50 dark:bg-gray-900"
        style={{
          backgroundImage: backgroundImage ? `url('${backgroundImage}')` : 'none',
        }}
      >
        <div className="absolute top-4 right-4 flex items-center gap-4">
          <LanguageSwitcher />
          <button
            className="p-2 bg-white rounded-full shadow-md dark:bg-gray-800"
            onClick={toggleTheme}
            aria-label="Toggle theme"
          >
            {isDarkMode ? '☀️' : '🌙'}
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full">
          <Link
            href="#"
            onClick={goBack}
            className="flex items-center text-blue-600 hover:underline mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t.auth.forgotPassword.back}
          </Link>

          <h1 className="text-3xl font-bold mb-2 text-center dark:text-white">
            {t.auth.forgotPassword.title}
          </h1>
          <p className="text-center text-gray-600 dark:text-gray-400 mb-6">
            {t.auth.forgotPassword.subtitle}
          </p>

          {success ? (
            <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-lg text-center mb-4">
              <p>{t.auth.forgotPassword.successMessage}</p>
              <p className="mt-4">
                <Link href="/auth/signin" className="text-blue-600 dark:text-blue-400 hover:underline">
                  {t.auth.forgotPassword.returnToLogin}
                </Link>
              </p>
            </div>
          ) : (
            <Form.Root onSubmit={handleSubmit} className="space-y-4">
              <Form.Field name="email">
                <div className="flex items-baseline justify-between mb-2">
                  <Form.Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t.auth.forgotPassword.email}
                  </Form.Label>
                  <Form.Message className="text-xs text-red-500" match="valueMissing">
                    {t.auth.forgotPassword.emailRequired}
                  </Form.Message>
                  <Form.Message className="text-xs text-red-500" match="typeMismatch">
                    {t.auth.forgotPassword.emailInvalid}
                  </Form.Message>
                </div>
                <Form.Control asChild>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={t.auth.forgotPassword.emailPlaceholder}
                  />
                </Form.Control>
              </Form.Field>

              {error && (
                <div className="text-red-500 text-sm text-center">{error}</div>
              )}

              <Form.Submit asChild>
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed mt-6"
                >
                  {loading ? t.auth.forgotPassword.sending : t.auth.forgotPassword.sendResetLink}
                </button>
              </Form.Submit>
            </Form.Root>
          )}
        </div>
      </div>
    </>
  );
}
