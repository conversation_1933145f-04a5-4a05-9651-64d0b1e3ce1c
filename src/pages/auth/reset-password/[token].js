import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
import { ArrowLeft, Eye, EyeOff } from 'lucide-react';
import * as Form from '@radix-ui/react-form';
import { useAuth } from '@/lib/contexts/AuthContext';

export default function ResetPassword() {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [backgroundImage, setBackgroundImage] = useState('');
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const router = useRouter();
  const { token } = router.query;
  const { resetPassword } = useAuth();

  useEffect(() => {
    // Check if user prefers dark mode
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setIsDarkMode(true);
    }

    // Set a random background image
    const images = [
      'https://source.unsplash.com/random/1920x1080/?abstract',
      'https://source.unsplash.com/random/1920x1080/?gradient',
      'https://source.unsplash.com/random/1920x1080/?pattern',
    ];
    setBackgroundImage(images[Math.floor(Math.random() * images.length)]);
  }, []);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      if (!token) {
        setError('Invalid reset token');
        return;
      }

      await resetPassword(token, formData.password);
      setSuccess(true);
      
      // Redirect to login page after 3 seconds
      setTimeout(() => {
        router.push('/auth/signin');
      }, 3000);
    } catch (err) {
      setError(err.message || 'An error occurred while resetting your password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Reset Password | TFLOUU</title>
      </Head>

      <div
        className="relative flex items-center justify-center min-h-screen bg-cover bg-center bg-gray-50 dark:bg-gray-900"
        style={{
          backgroundImage: backgroundImage ? `url('${backgroundImage}')` : 'none',
        }}
      >
        <div className="absolute top-4 right-4">
          <button
            className="p-2 bg-white rounded-full shadow-md dark:bg-gray-800"
            onClick={toggleTheme}
            aria-label="Toggle theme"
          >
            {isDarkMode ? '☀️' : '🌙'}
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full">
          <Link
            href="/auth/signin"
            className="flex items-center text-blue-600 hover:underline mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to login
          </Link>

          <h1 className="text-3xl font-bold mb-2 text-center dark:text-white">
            Reset Password
          </h1>
          <p className="text-center text-gray-600 dark:text-gray-400 mb-6">
            Create a new password for your account
          </p>

          {success ? (
            <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-lg text-center mb-4">
              <p>Password has been reset successfully!</p>
              <p className="mt-2">Redirecting to login page...</p>
            </div>
          ) : (
            <Form.Root onSubmit={handleSubmit} className="space-y-4">
              <Form.Field name="password">
                <div className="flex items-baseline justify-between mb-2">
                  <Form.Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    New Password
                  </Form.Label>
                  <Form.Message className="text-xs text-red-500" match="valueMissing">
                    Please enter a password
                  </Form.Message>
                </div>
                <div className="relative">
                  <Form.Control asChild>
                    <input
                      type={showPassword ? "text" : "password"}
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      required
                      minLength={8}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="••••••••"
                    />
                  </Form.Control>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400"
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </Form.Field>

              <Form.Field name="confirmPassword">
                <div className="flex items-baseline justify-between mb-2">
                  <Form.Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Confirm Password
                  </Form.Label>
                  <Form.Message className="text-xs text-red-500" match="valueMissing">
                    Please confirm your password
                  </Form.Message>
                </div>
                <Form.Control asChild>
                  <input
                    type={showPassword ? "text" : "password"}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="••••••••"
                  />
                </Form.Control>
              </Form.Field>

              {error && (
                <div className="text-red-500 text-sm text-center">{error}</div>
              )}

              <Form.Submit asChild>
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed mt-6"
                >
                  {loading ? "Resetting..." : "Reset Password"}
                </button>
              </Form.Submit>
            </Form.Root>
          )}
        </div>
      </div>
    </>
  );
}
