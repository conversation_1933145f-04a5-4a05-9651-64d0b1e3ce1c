import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { signIn } from "next-auth/react";
import { CheckCircle, XCircle } from 'lucide-react';

export default function Verify() {
  const router = useRouter();
  const { token } = router.query;
  const [status, setStatus] = useState('loading'); // loading, success, error
  const [message, setMessage] = useState('');

  useEffect(() => {
    const verifyToken = async () => {
      if (!token) return;

      try {
        const response = await fetch('/api/auth/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        const data = await response.json();

        if (response.ok && data.success) {
          setStatus('success');
          setMessage(data.message);
          
          // Başarılı doğrulama sonrası otomatik login ve yönlendirme
          setTimeout(() => {
            router.push('/auth/signin?verified=true');
          }, 2000);
        } else {
          setStatus('error');
          setMessage(data.message || 'Verification failed');
        }
      } catch (error) {
        setStatus('error');
        setMessage('An error occurred during verification');
      }
    };

    verifyToken();
  }, [token, router]);

  return (
    <>
      <Head>
        <title>Email Verification | TFLOUU</title>
      </Head>

      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 bg-white dark:bg-gray-800 p-8 rounded-lg shadow">
          {status === 'loading' && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
              <h2 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
                Verifying your email...
              </h2>
            </div>
          )}

          {status === 'success' && (
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto" />
              <h2 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
                Email Verified Successfully!
              </h2>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                {message}
              </p>
              <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                Redirecting to sign in page...
              </p>
            </div>
          )}

          {status === 'error' && (
            <div className="text-center">
              <XCircle className="h-12 w-12 text-red-500 mx-auto" />
              <h2 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">
                Verification Failed
              </h2>
              <p className="mt-2 text-sm text-red-500">
                {message}
              </p>
              <button
                onClick={() => router.push('/auth/signin')}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Go to Sign In
              </button>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
