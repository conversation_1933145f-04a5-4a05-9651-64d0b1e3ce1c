import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Head from 'next/head';
import { useAuth } from '@/lib/contexts/AuthContext';
import { Eye, EyeOff, ArrowLeft } from 'lucide-react';
import * as Form from '@radix-ui/react-form';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';
import { en } from '@/translations/en';
import { tr } from '@/translations/tr';
import SignupSuccessDialog from "@/components/auth/SignupSuccessDialog";

export default function Signup() {
  const router = useRouter();
  const { locale } = router;
  const t = locale === 'tr' ? tr : en;
  const { register, loginWithGoogle, update } = useAuth();
  
  // States
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [backgroundImage, setBackgroundImage] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  const [registrationResult, setRegistrationResult] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });

  useEffect(() => {
    // Check system preference for dark mode
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setIsDarkMode(true);
      document.documentElement.classList.add('dark');
    }

    // Set random background image safely
    try {
      if (imgUri && imgUri.length > 0) {
        setBackgroundImage(imgUri[Math.floor(Math.random() * imgUri.length)]);
      }
    } catch (err) {
      console.error('Error setting background image:', err);
    }
  }, []);


const handleSuccessDialogClose = () => {
  setShowSuccess(false);
  if (registrationResult?.isInvited) {
    router.push('/app/dashboard');
  } else {
    router.push('/auth/signin');
  }
};

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    if (!isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    // console.log('name, value:', name, value, formData);
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    try {
      setLoading(true);
      setError('');

      const result = await register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
      });

      if (!result.success) {
        setError(result.message || 'Registration failed');
        return;
      }

      setRegistrationResult(result);
      setShowSuccess(true);

      if (result.isInvited && result.token && result.refreshToken) {
        const loginResult = await signIn("credentials", {
          email: formData.email,
          password: formData.password,
          redirect: false,
        });

        if (!loginResult.error) {
          const session = {
            user: {
              ...result.user,
              token: result.token,
              refreshToken: result.refreshToken
            }
          };
          await update(session);
        }
      }
    } catch (err) {
      setError(err.message || 'An error occurred during registration');
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await loginWithGoogle();
    } catch (err) {
      setError('Failed to sign in with Google');
    }
  };
  const goBack = (event) => {
    event.preventDefault();
    router.back();
  }

  return (
    <>
      <Head>
        <title key="title">{(t.auth.signup.title ? t.auth.signup.title : '') + ' | TFLOUU'}</title>
      </Head>

      <div
        className="relative flex items-center justify-center min-h-screen bg-cover bg-center bg-gray-50 dark:bg-gray-900"
        style={{
          backgroundImage: backgroundImage ? `url('${backgroundImage}')` : 'none',
        }}
      >
        <div className="absolute top-4 right-4 flex items-center gap-4">
          <LanguageSwitcher />
          <button
            className="p-2 bg-white rounded-full shadow-md dark:bg-gray-800"
            onClick={toggleTheme}
            aria-label="Toggle theme"
          >
            {isDarkMode ? '☀️' : '🌙'}
          </button>
        </div>

        <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg max-w-md w-full">
          <Link
            href="#"
            onClick={goBack} 
            className="flex items-center text-blue-600 hover:underline mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t.auth.signup.back}
          </Link>

          <h1 className="text-2xl font-bold mb-2 text-center dark:text-white">
            {t.auth.signup.title}
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 text-center mb-6">
            {t.auth.signup.subtitle}
          </p>

          <div className="my-6">
            <button
              onClick={handleGoogleSignIn}
              className="w-full flex items-center justify-center gap-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 px-6 py-3 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-300"
            >
              <img
                src="https://www.google.com/favicon.ico"
                alt="Google"
                className="w-5 h-5"
              />
              {t.auth.signup.signupWithGoogle}
            </button>
          </div>

          <div className="relative flex items-center justify-center mb-6">
            <div className="border-t border-gray-300 dark:border-gray-600 w-full"></div>
            <span className="bg-white dark:bg-gray-800 px-3 text-sm text-gray-500 dark:text-gray-400 absolute">
              {t.auth.signup.or}
            </span>
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-lg mb-6 text-sm">
              {error}
            </div>
          )}


          <Form.Root onSubmit={handleSubmit} className="space-y-4">
            <Form.Field name="name">
              <div className="flex items-baseline justify-between mb-2">
                <Form.Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t.auth.signup.fullName}
                </Form.Label>
                <Form.Message className="text-xs text-red-500" match="valueMissing">
                  {t.auth.signup.fullNameRequired}
                </Form.Message>
              </div>
              <Form.Control asChild>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  autoComplete = "off"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={t.auth.signup.fullNamePlaceholder}
                />
              </Form.Control>
            </Form.Field>

            <Form.Field name="email">
              <div className="flex items-baseline justify-between mb-2">
                <Form.Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t.auth.signup.email}
                </Form.Label>
                <Form.Message className="text-xs text-red-500" match="valueMissing">
                  {t.auth.signup.emailRequired}
                </Form.Message>
                <Form.Message className="text-xs text-red-500" match="typeMismatch">
                  {t.auth.signup.emailInvalid}
                </Form.Message>
              </div>
              <Form.Control asChild>
                <input
                  type="email"
                  name="email"
                  autoComplete="off"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={t.auth.signup.emailPlaceholder}
                />
              </Form.Control>
            </Form.Field>

            <Form.Field name="password">
              <div className="flex items-baseline justify-between mb-2">
                <Form.Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t.auth.signup.password}
                </Form.Label>
                <Form.Message className="text-xs text-red-500" match="valueMissing">
                  {t.auth.signup.passwordRequired}
                </Form.Message>
                <Form.Message className="text-xs text-red-500" match="tooShort">
                  {t.auth.signup.passwordMinLength}
                </Form.Message>
              </div>
              <div className="relative">
                <Form.Control asChild>
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    autoComplete="off"
                    minLength={8}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder={t.auth.signup.passwordPlaceholder}
                  />
                </Form.Control>
                <button
                  type="button"
                  className="absolute right-3 top-3 text-gray-500 dark:text-gray-400"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </Form.Field>

            <Form.Field name="confirmPassword">
              <div className="flex items-baseline justify-between mb-2">
                <Form.Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t.auth.signup.confirmPassword}
                </Form.Label>
                <Form.Message className="text-xs text-red-500" match="valueMissing">
                  {t.auth.signup.confirmPasswordRequired}
                </Form.Message>
              </div>
              <Form.Control asChild>
                <input
                  type={showPassword ? "text" : "password"}
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                  autoComplete="off"
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={t.auth.signup.confirmPasswordPlaceholder}
                />
              </Form.Control>
            </Form.Field>

            <Form.Submit asChild>
              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed mt-6"
              >
                {loading ? t.auth.signup.creatingAccount : t.auth.signup.createAccountButton}
              </button>
            </Form.Submit>
          </Form.Root>

          <SignupSuccessDialog
            open={showSuccess}
            onClose={handleSuccessDialogClose}
            isInvited={registrationResult?.isInvited}
          />

          <p className="mt-4 text-center text-sm text-gray-600 dark:text-gray-400">
            {t.auth.signup.privacyNotice}{' '}
            <a href="#" className="text-indigo-600 hover:underline">
              {t.auth.signup.privacyPolicy}
            </a>{' '}
            {t.auth.signup.and}{' '}
            <a href="#" className="text-indigo-600 hover:underline">
              {t.auth.signup.termsOfService}
            </a>
          </p>
          <p className="mt-4 text-center text-sm text-gray-600 dark:text-gray-400">
            {t.auth.signup.alreadyHaveAccount}{' '}
            <Link href="./signin" className="text-blue-600 hover:underline">
              {t.auth.signup.signInLink}
            </Link>
          </p>
        </div>
      </div>
    </>
  );
}

const imgUri = [
  'https://images.unsplash.com/photo-**********-4b4a1ae0f04d?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-**********-823330fc2551?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-*************-cf7c8fe21800?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-*************-dfb7085fc667?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-**********-0adf9ea622e2?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1578916171728-46686eac8d58?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1598357850706-0188bc0372b2?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1494891848038-7bd202a2afeb?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1584968153986-3f5fe523b044?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1585854467604-cf2080ccef31?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1490365728022-deae76380607?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=800&q=80',
  'https://images.unsplash.com/photo-1501492751416-51484c0d911f?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHw%3D&auto=format&fit=crop&w=1024&q=80',
];

